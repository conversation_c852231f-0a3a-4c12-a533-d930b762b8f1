
import pandas as pd
import torch
from transformers import <PERSON><PERSON>okenizer, BertForSequenceClassification
from torch.optim import AdamW
import warnings
warnings.filterwarnings('ignore')

def simple_bert_demo():
    """Simple BERT demonstration"""
    print("Loading BERT model...")
    
    # Force CPU usage
    device = torch.device('cpu')
    
    # Load tokenizer and model
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    model = BertForSequenceClassification.from_pretrained('bert-base-uncased', num_labels=3)
    model.to(device)
    
    # Sample data
    texts = [
        "oil change service maintenance",
        "brake pad replacement repair", 
        "battery test and diagnosis"
    ]
    labels = [0, 1, 2]  # Maintenance, Repair, Diagnosis
    
    print("Tokenizing texts...")
    # Tokenize
    encodings = tokenizer(texts, truncation=True, padding=True, return_tensors='pt')
    
    print("Running inference...")
    # Simple inference
    model.eval()
    with torch.no_grad():
        outputs = model(**encodings)
        predictions = torch.argmax(outputs.logits, dim=-1)
    
    print("Results:")
    for i, (text, pred) in enumerate(zip(texts, predictions)):
        print(f"  Text: {text}")
        print(f"  Prediction: {pred.item()}")
        print()
    
    print("✓ BERT demo completed successfully!")
    return True

if __name__ == "__main__":
    try:
        simple_bert_demo()
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
