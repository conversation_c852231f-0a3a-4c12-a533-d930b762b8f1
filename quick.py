def quicksort1(arr):
    if len(arr) <= 1:
        return arr   # Base case: already sorted
    pivot = arr[0] 
    left = [x for x in arr[1:] if x <= pivot]
    print("left",left)
    right = [x for x in arr[1:] if x > pivot]
    print("right",right)
    #print(quicksort(left) + [pivot] + quicksort(right))
    a= quicksort1(left) + [pivot] + quicksort1(right)
    return a
result = quicksort1([8, 2, 11, 9, 0])
print("Sorted Result:", result)