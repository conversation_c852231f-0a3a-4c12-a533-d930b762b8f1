# =============================================================================
# ENHANCED CELL 4B: Detailed Classification Report and Analysis
# =============================================================================
# Add this cell after your training cell (Cell 4) and before saving (Cell 5)

def generate_detailed_classification_report(model, tokenizer, input_data, output_data, 
                                          label_mapping, category_mapping, num_classes):
    """Generate comprehensive classification report with detailed metrics"""
    
    import matplotlib.pyplot as plt
    import seaborn as sns
    from sklearn.metrics import (
        classification_report, confusion_matrix, 
        precision_recall_fscore_support, accuracy_score
    )
    import pandas as pd
    
    print(f"\n{'='*60}")
    print("📊 COMPREHENSIVE CLASSIFICATION REPORT")
    print(f"{'='*60}")
    
    # Prepare data for evaluation
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # Split data for evaluation
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    # Create test dataset
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    # Get predictions
    all_predictions = []
    all_true_labels = []
    all_probabilities = []
    
    print("🔍 Generating predictions for detailed analysis...")
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            # Get predictions and probabilities
            predictions = torch.argmax(logits, dim=-1)
            probabilities = torch.softmax(logits, dim=-1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_true_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
    
    # Convert to numpy arrays
    y_true = np.array(all_true_labels)
    y_pred = np.array(all_predictions)
    y_prob = np.array(all_probabilities)
    
    # Calculate overall metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision, recall, f1, support = precision_recall_fscore_support(y_true, y_pred, average='weighted')
    
    print(f"\n📈 OVERALL PERFORMANCE METRICS")
    print(f"{'='*40}")
    print(f"🎯 Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"🎯 Precision: {precision:.4f}")
    print(f"🎯 Recall:    {recall:.4f}")
    print(f"🎯 F1-Score:  {f1:.4f}")
    print(f"📊 Total Test Samples: {len(y_true)}")
    
    # Detailed classification report
    print(f"\n📋 DETAILED CLASSIFICATION REPORT")
    print(f"{'='*60}")
    
    # Create target names using category mapping
    target_names = []
    reverse_label_mapping = {v: k for k, v in label_mapping.items()}
    
    for i in range(num_classes):
        original_label = reverse_label_mapping[i]
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                target_names.append(f"{original_label}-{category_name}")
            except:
                target_names.append(f"Category_{original_label}")
        else:
            target_names.append(f"Category_{original_label}")
    
    # Generate classification report
    report = classification_report(
        y_true, y_pred, 
        target_names=target_names,
        output_dict=True,
        zero_division=0
    )
    
    # Print formatted report
    report_str = classification_report(
        y_true, y_pred, 
        target_names=target_names,
        zero_division=0
    )
    print(report_str)
    
    # Create detailed DataFrame for analysis
    print(f"\n📊 PER-CLASS PERFORMANCE ANALYSIS")
    print(f"{'='*80}")
    
    class_metrics = []
    for i, class_name in enumerate(target_names):
        if str(i) in report:
            metrics = report[str(i)]
            class_metrics.append({
                'Class': class_name,
                'Precision': f"{metrics['precision']:.3f}",
                'Recall': f"{metrics['recall']:.3f}",
                'F1-Score': f"{metrics['f1-score']:.3f}",
                'Support': int(metrics['support'])
            })
    
    df_metrics = pd.DataFrame(class_metrics)
    print(df_metrics.to_string(index=False))
    
    # Confusion Matrix Analysis
    print(f"\n🔍 CONFUSION MATRIX ANALYSIS")
    print(f"{'='*40}")
    
    cm = confusion_matrix(y_true, y_pred)
    
    # Find best and worst performing classes
    class_accuracies = []
    for i in range(len(cm)):
        if cm[i].sum() > 0:  # Avoid division by zero
            class_acc = cm[i, i] / cm[i].sum()
            class_accuracies.append((i, class_acc, target_names[i]))
    
    # Sort by accuracy
    class_accuracies.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🏆 TOP 5 BEST PERFORMING CLASSES:")
    for i, (class_idx, acc, name) in enumerate(class_accuracies[:5]):
        print(f"  {i+1}. {name}: {acc:.3f} ({acc*100:.1f}%)")
    
    print(f"\n⚠️ TOP 5 WORST PERFORMING CLASSES:")
    for i, (class_idx, acc, name) in enumerate(class_accuracies[-5:]):
        print(f"  {i+1}. {name}: {acc:.3f} ({acc*100:.1f}%)")
    
    # Most confused pairs
    print(f"\n🤔 MOST CONFUSED CLASS PAIRS:")
    confused_pairs = []
    for i in range(len(cm)):
        for j in range(len(cm)):
            if i != j and cm[i, j] > 0:
                confused_pairs.append((cm[i, j], target_names[i], target_names[j]))
    
    confused_pairs.sort(reverse=True)
    for count, true_class, pred_class in confused_pairs[:5]:
        print(f"  {true_class} → {pred_class}: {count} times")
    
    # Confidence Analysis
    print(f"\n🎯 PREDICTION CONFIDENCE ANALYSIS")
    print(f"{'='*40}")
    
    max_probs = np.max(y_prob, axis=1)
    correct_predictions = (y_true == y_pred)
    
    avg_confidence_correct = np.mean(max_probs[correct_predictions])
    avg_confidence_incorrect = np.mean(max_probs[~correct_predictions])
    
    print(f"📊 Average confidence for correct predictions: {avg_confidence_correct:.3f}")
    print(f"📊 Average confidence for incorrect predictions: {avg_confidence_incorrect:.3f}")
    print(f"📊 Confidence difference: {avg_confidence_correct - avg_confidence_incorrect:.3f}")
    
    # High/Low confidence predictions
    high_conf_threshold = 0.9
    low_conf_threshold = 0.5
    
    high_conf_correct = np.sum((max_probs > high_conf_threshold) & correct_predictions)
    high_conf_total = np.sum(max_probs > high_conf_threshold)
    low_conf_predictions = np.sum(max_probs < low_conf_threshold)
    
    print(f"📊 High confidence (>{high_conf_threshold}) predictions: {high_conf_total}")
    print(f"📊 High confidence correct: {high_conf_correct} ({high_conf_correct/max(high_conf_total,1)*100:.1f}%)")
    print(f"📊 Low confidence (<{low_conf_threshold}) predictions: {low_conf_predictions}")
    
    # Sample predictions analysis
    print(f"\n🔍 SAMPLE PREDICTIONS ANALYSIS")
    print(f"{'='*50}")
    
    # Get some sample texts for analysis
    sample_indices = np.random.choice(len(X_test), min(5, len(X_test)), replace=False)
    
    for idx in sample_indices:
        text = X_test[idx]
        true_label = y_test[idx]
        pred_label = y_pred[idx]
        confidence = max_probs[idx]
        
        true_class_name = target_names[true_label]
        pred_class_name = target_names[pred_label]
        
        status = "✅ CORRECT" if true_label == pred_label else "❌ INCORRECT"
        
        print(f"\n📝 Text: {text[:100]}...")
        print(f"🏷️ True: {true_class_name}")
        print(f"🤖 Predicted: {pred_class_name}")
        print(f"📊 Confidence: {confidence:.3f}")
        print(f"🎯 Status: {status}")
        print("-" * 50)
    
    # Save detailed report
    report_data = {
        'overall_metrics': {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        },
        'per_class_metrics': class_metrics,
        'confusion_matrix': cm.tolist(),
        'class_names': target_names,
        'confidence_analysis': {
            'avg_confidence_correct': avg_confidence_correct,
            'avg_confidence_incorrect': avg_confidence_incorrect,
            'high_confidence_total': int(high_conf_total),
            'high_confidence_correct': int(high_conf_correct),
            'low_confidence_total': int(low_conf_predictions)
        }
    }
    
    return report_data, accuracy

# Generate the detailed classification report
print("🚀 Generating comprehensive classification report...")
report_data, final_accuracy = generate_detailed_classification_report(
    model, tokenizer, input_data, output_data, 
    label_mapping, category_mapping, num_classes
)

print(f"\n🎉 CLASSIFICATION REPORT COMPLETE!")
print(f"📊 Final Model Accuracy: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")

# Optional: Save report to file
import json
with open("/kaggle/working/classification_report.json", 'w') as f:
    json.dump(report_data, f, indent=2)

print(f"💾 Detailed report saved to: /kaggle/working/classification_report.json")
