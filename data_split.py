import pandas as pd
from sklearn.model_selection import train_test_split

def split_excel_for_bert(file_path, sheet_name, text_column, label_column, train_excel_path, test_excel_path):
    # Load the data
    # Drop missing or empty
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    df = df.dropna(subset=[text_column, label_column])
    df[text_column] = df[text_column].astype(str).str.strip()
    df = df[df[text_column] != '']

    # Filter out rare classes (only 1 sample)
    label_counts = df[label_column].value_counts()
    df = df[df[label_column].isin(label_counts[label_counts > 1].index)]

    # Split
    train_df, test_df = train_test_split(
        df, test_size=0.3, random_state=42, stratify=df[label_column]
    )

    # Save to Excel files
    train_df.to_excel(train_excel_path, index=False, engine='openpyxl')
    test_df.to_excel(test_excel_path, index=False, engine='openpyxl')

    print(f"✅ Train set saved to: {train_excel_path} ({len(train_df)} rows)")
    print(f"✅ Test set saved to: {test_excel_path} ({len(test_df)} rows)")

# Example usage
split_excel_for_bert(
    file_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/archive_list_inal.xlsx",
    sheet_name="MergedSheet",
    text_column="labor_description_str",
    label_column="category_num",
    train_excel_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data.xlsx",
    test_excel_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/test_data.xlsx"
)
