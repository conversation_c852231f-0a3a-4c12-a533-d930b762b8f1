# Labor Description Classification Project

## Overview
This project implements a machine learning model to classify labor descriptions into predefined categories. The model uses TF-IDF (Term Frequency-Inverse Document Frequency) vectorization combined with Logistic Regression to achieve accurate text classification.

## Data Preparation
1. **Input Data**: `labor_description_str` - Text descriptions of labor activities
2. **Output Data**: `category_num` - Numerical category labels (0-21)
3. **Data Source**: Excel file `archive_list_inal.xlsx` with MergedSheet containing 199,885 samples

## Category Mapping
The model classifies labor descriptions into 22 categories:

| Category Number | Category Label |
|----------------|----------------|
| 0 | Maint/Service |
| 1 | Diagnosis |
| 2 | Battery |
| 3 | Tire |
| 4 | Wiper Blade/Arm/Insert |
| 5 | Alignment |
| 6 | Brake - Pad/Rotor/L/S/D |
| 7 | Key Blank |
| 8 | Bulb |
| 9 | Mount and Balance (BMW) |
| 10 | MISC |
| 11 | Belt - Drive |
| 12 | Mechanical + Body |
| 13 | *Not Classified (Exclude) |
| 14 | Accessory |
| 15 | Spark Plug |
| 16 | Hoses |
| 17 | Body Collision |
| 18 | Transmission Assembly |
| 19 | Fluid (All Kinds) |
| 20 | Engine Assembly |
| 21 | Seat Belt |

## Model Performance
- **Algorithm**: Logistic Regression with TF-IDF vectorization
- **Accuracy**: 68.72% on test set (39,977 samples)
- **Features**: 5,000 TF-IDF features with n-grams (1,2)
- **Training Data**: 159,907 samples
- **Test Data**: 39,977 samples

## Files Description

### 1. `sheet_merge.py`
- Original script for merging Excel sheets and creating category numbers
- Maps `labor_category_label` to numerical `category_num` using pandas factorize

### 2. `simple_bert_training.py`
- Main training script that:
  - Loads and preprocesses data from Excel file
  - Creates TF-IDF features from text descriptions
  - Trains Logistic Regression classifier
  - Evaluates model performance
  - Saves trained model to pickle file

### 3. `test_model.py`
- Interactive testing script that:
  - Loads the trained model
  - Allows user to input labor descriptions
  - Provides predictions with confidence scores
  - Shows top 3 predictions for each input

### 4. `labor_classifier_model.pkl`
- Saved model file containing:
  - Trained Logistic Regression model
  - TF-IDF vectorizer
  - Category mapping
  - Model metadata

## Usage

### Training the Model
```bash
python3 simple_bert_training.py
```

### Testing the Model
```bash
python3 test_model.py
```

### Example Predictions
- **"oil change service"** → Maint/Service (94.86% confidence)
- **"brake pad replacement"** → Brake - Pad/Rotor/L/S/D (81.55% confidence)
- **"battery test and diagnosis"** → MISC (63.41% confidence)

## Technical Details

### Data Preprocessing
- Text conversion to string format
- Removal of empty descriptions
- Filtering out categories with insufficient samples (< 2 samples)

### Feature Engineering
- TF-IDF vectorization with:
  - Maximum 5,000 features
  - English stop words removal
  - N-grams: unigrams and bigrams
  - Lowercase conversion

### Model Training
- Algorithm: Logistic Regression
- Maximum iterations: 1,000
- Random state: 42 for reproducibility
- No stratification due to class imbalance

## Requirements
- pandas
- scikit-learn
- openpyxl
- pickle (built-in)

## Installation
```bash
pip install pandas scikit-learn openpyxl
```

## Future Improvements
1. **BERT Implementation**: The original plan included BERT-based classification, which could potentially improve accuracy
2. **Class Imbalance**: Address the significant class imbalance (MISC category has 84,311 samples vs others with much fewer)
3. **Feature Engineering**: Experiment with different text preprocessing techniques
4. **Ensemble Methods**: Combine multiple models for better performance
5. **Hyperparameter Tuning**: Optimize model parameters using grid search or random search

## Notes
- The model shows good performance for well-represented categories
- Some categories with very few samples (like "Seat Belt" with only 1 sample) were filtered out
- The MISC category tends to be over-predicted due to its large representation in the training data
