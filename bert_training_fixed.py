import pandas as pd
import numpy as np
import torch
import os
import warnings

# Suppress CUDA warnings and force CPU usage
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"
warnings.filterwarnings('ignore')

# Force CPU usage
torch.cuda.is_available = lambda: False

try:
    from transformers import (
        BertTokenizer, 
        BertForSequenceClassification, 
        get_linear_schedule_with_warmup
    )
    from torch.optim import AdamW
    from torch.utils.data import Dataset, DataLoader
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    from tqdm import tqdm
    
    TRANSFORMERS_AVAILABLE = True
    print("✓ Transformers library loaded successfully")
except ImportError as e:
    print(f"✗ Error importing transformers: {e}")
    print("Falling back to TF-IDF approach...")
    TRANSFORMERS_AVAILABLE = False

def load_and_prepare_data(file_path):
    """Load data from Excel file and prepare for training"""
    print("Loading data from Excel file...")
    df = pd.read_excel(file_path, sheet_name="MergedSheet")
    
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Check if required columns exist
    if 'labor_description_str' not in df.columns:
        raise ValueError("Column 'labor_description_str' not found in the data")
    if 'category_num' not in df.columns:
        raise ValueError("Column 'category_num' not found in the data")
    
    # Clean the data
    df = df.dropna(subset=['labor_description_str', 'category_num'])
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    
    # Get input and output data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    print(f"Number of samples: {len(input_data)}")
    print(f"Number of unique categories: {len(set(output_data))}")
    
    # Display category mapping
    category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
    print("\nCategory Mapping:")
    for _, row in category_mapping.iterrows():
        print(f"  {row['category_num']}: {row['labor_category_label']}")
    
    return input_data, output_data, len(set(output_data)), category_mapping

class LaborDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def train_bert_model_simple(input_data, output_data, num_classes, max_samples=10000):
    """Train BERT model with reduced complexity for demonstration"""
    
    if not TRANSFORMERS_AVAILABLE:
        print("BERT training not available. Please use simple_bert_training.py instead.")
        return None, None, 0, "N/A"
    
    print("Starting BERT model training (simplified version)...")
    
    # Limit data size for faster training
    if len(input_data) > max_samples:
        print(f"Limiting training data to {max_samples} samples for faster training...")
        indices = np.random.choice(len(input_data), max_samples, replace=False)
        input_data = [input_data[i] for i in indices]
        output_data = [output_data[i] for i in indices]
    
    # Set device to CPU
    device = torch.device('cpu')
    print(f"Using device: {device}")
    
    # Initialize tokenizer and model
    model_name = 'bert-base-uncased'
    print(f"Loading {model_name}...")
    
    try:
        tokenizer = BertTokenizer.from_pretrained(model_name)
        model = BertForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=num_classes
        ).to(device)
        print("✓ BERT model loaded successfully")
    except Exception as e:
        print(f"✗ Error loading BERT model: {e}")
        return None, None, 0, "Error"
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(X_train)}")
    print(f"Testing samples: {len(X_test)}")
    
    # Create datasets with smaller batch size
    batch_size = 4  # Very small batch size for CPU training
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer with lower learning rate
    optimizer = AdamW(model.parameters(), lr=1e-5)
    
    # Training loop - just 1 epoch for demonstration
    epochs = 1
    model.train()
    
    print(f"\nTraining for {epochs} epoch(s)...")
    
    for epoch in range(epochs):
        total_loss = 0
        num_batches = min(10, len(train_loader))  # Limit to 10 batches for demo
        
        print(f"Epoch {epoch + 1}/{epochs} - Processing {num_batches} batches...")
        
        for i, batch in enumerate(train_loader):
            if i >= num_batches:
                break
                
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            if (i + 1) % 2 == 0:
                print(f"  Batch {i+1}/{num_batches}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        print(f"Average training loss: {avg_loss:.4f}")
    
    # Simple evaluation on a few test samples
    model.eval()
    correct = 0
    total = 0
    max_eval_batches = 5  # Limit evaluation for demo
    
    print(f"\nEvaluating on {max_eval_batches} batches...")
    
    with torch.no_grad():
        for i, batch in enumerate(test_loader):
            if i >= max_eval_batches:
                break
                
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            predictions = torch.argmax(outputs.logits, dim=-1)
            
            correct += (predictions == labels).sum().item()
            total += labels.size(0)
    
    accuracy = correct / total if total > 0 else 0
    print(f"Accuracy on {total} samples: {accuracy:.4f}")
    
    # Save model
    try:
        model.save_pretrained('./bert_labor_classifier_simple')
        tokenizer.save_pretrained('./bert_labor_classifier_simple')
        print("Model saved to './bert_labor_classifier_simple'")
    except Exception as e:
        print(f"Error saving model: {e}")
    
    return model, tokenizer, 0, accuracy

if __name__ == "__main__":
    file_path = "archive_list_inal.xlsx"
    
    try:
        # Load and prepare data
        input_data, output_data, num_classes, category_mapping = load_and_prepare_data(file_path)
        
        if TRANSFORMERS_AVAILABLE:
            print("\n" + "="*50)
            print("BERT TRAINING (SIMPLIFIED)")
            print("="*50)
            
            # Train BERT model with limited data
            model, tokenizer, train_losses, accuracy = train_bert_model_simple(
                input_data, output_data, num_classes, max_samples=1000
            )
            
            if model is not None:
                print(f"\nBERT Training completed!")
                print(f"Final accuracy: {accuracy:.4f}")
                print("Note: This is a simplified demonstration with limited data and epochs.")
            else:
                print("BERT training failed. Please check the error messages above.")
        else:
            print("\nBERT training not available due to import errors.")
            print("Please use 'simple_bert_training.py' for TF-IDF based classification.")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
