# 🚀 Kaggle-Friendly BERT Training Guide

## 📋 Quick Setup Checklist

### ✅ **Step 1: Kaggle Notebook Setup**
1. Go to [Kaggle.com](https://www.kaggle.com) and create/login to your account
2. Click "Create" → "New Notebook"
3. **IMPORTANT**: Turn on GPU accelerator:
   - Click "Settings" (gear icon) in the right panel
   - Under "Accelerator", select **"GPU P100"** or **"GPU T4 x2"**
   - Click "Save"

### ✅ **Step 2: Upload Your Data**
1. Click "Add Data" → "Upload" → "New Dataset"
2. Upload your `archive_list_inal.xlsx` file
3. Set dataset title: "Labor Description Data"
4. Make it public or private as needed
5. Click "Create Dataset"

### ✅ **Step 3: Add Data to Notebook**
1. In your notebook, click "Add Data" 
2. Search for your uploaded dataset
3. Click "Add" to include it in your notebook

---

## 🔧 **Method 1: Single Script (Recommended)**

Copy and paste this complete script into a single Kaggle cell:

```python
# COMPLETE KAGGLE BERT TRAINING SCRIPT
import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter

# Kaggle settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from tqdm.auto import tqdm

class LaborDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text, truncation=True, padding='max_length',
            max_length=self.max_length, return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

# Find and load data
data_files = []
for root, dirs, files in os.walk("/kaggle/input"):
    for file in files:
        if file.endswith('.xlsx'):
            data_files.append(os.path.join(root, file))

print("📁 Found data files:", data_files)
df = pd.read_excel(data_files[0], sheet_name="MergedSheet")
print(f"📊 Data shape: {df.shape}")

# Prepare data
df = df.dropna(subset=['labor_description_str', 'category_num'])
df['labor_description_str'] = df['labor_description_str'].astype(str)

input_data = df['labor_description_str'].tolist()
output_data = df['category_num'].tolist()

# Filter classes with insufficient samples
class_counts = Counter(output_data)
valid_classes = [cls for cls, count in class_counts.items() if count >= 2]

filtered_input = []
filtered_output = []
for i, label in enumerate(output_data):
    if label in valid_classes:
        filtered_input.append(input_data[i])
        filtered_output.append(label)

# Create label mapping
label_mapping = {old: new for new, old in enumerate(sorted(valid_classes))}
mapped_output = [label_mapping[label] for label in filtered_output]

print(f"✅ Prepared: {len(filtered_input)} samples, {len(valid_classes)} classes")

# Limit data for Kaggle (memory constraints)
MAX_SAMPLES = 5000
if len(filtered_input) > MAX_SAMPLES:
    indices = np.random.choice(len(filtered_input), MAX_SAMPLES, replace=False)
    filtered_input = [filtered_input[i] for i in indices]
    mapped_output = [mapped_output[i] for i in indices]

# Setup device and model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🖥️ Using device: {device}")

tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
model = AutoModelForSequenceClassification.from_pretrained(
    'bert-base-uncased', num_labels=len(valid_classes)
).to(device)

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    filtered_input, mapped_output, test_size=0.2, random_state=42
)

# Create datasets
train_dataset = LaborDataset(X_train, y_train, tokenizer)
test_dataset = LaborDataset(X_test, y_test, tokenizer)

train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)

# Training
optimizer = AdamW(model.parameters(), lr=2e-5)
model.train()

EPOCHS = 2
for epoch in range(EPOCHS):
    print(f"\n📖 Epoch {epoch + 1}/{EPOCHS}")
    total_loss = 0
    
    for batch in tqdm(train_loader, desc="Training"):
        optimizer.zero_grad()
        
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
    
    print(f"📉 Average loss: {total_loss/len(train_loader):.4f}")

# Evaluation
model.eval()
predictions = []
true_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)
        predictions.extend(torch.argmax(outputs.logits, dim=-1).cpu().numpy())
        true_labels.extend(labels.cpu().numpy())

accuracy = accuracy_score(true_labels, predictions)
print(f"\n🎯 Test Accuracy: {accuracy:.4f}")

# Save model
save_dir = "/kaggle/working/bert_labor_classifier"
os.makedirs(save_dir, exist_ok=True)

model.save_pretrained(save_dir)
tokenizer.save_pretrained(save_dir)

# Save metadata
metadata = {
    'label_mapping': label_mapping,
    'accuracy': float(accuracy),
    'num_classes': len(valid_classes)
}

with open(f"{save_dir}/metadata.json", 'w') as f:
    json.dump(metadata, f, indent=2)

with open(f"{save_dir}/model_data.pkl", 'wb') as f:
    pickle.dump(metadata, f)

print(f"✅ Model saved to: {save_dir}")
print(f"🎉 Training completed! Accuracy: {accuracy:.4f}")

# Test predictions
test_texts = [
    "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
    "BATTERY TEST AND REPLACEMENT",
    "BRAKE PAD REPLACEMENT"
]

reverse_mapping = {v: k for k, v in label_mapping.items()}

for text in test_texts:
    encoding = tokenizer(text, truncation=True, padding='max_length', 
                        max_length=128, return_tensors='pt').to(device)
    
    with torch.no_grad():
        outputs = model(**encoding)
        pred = torch.argmax(outputs.logits, dim=-1).item()
        conf = torch.softmax(outputs.logits, dim=-1).max().item()
    
    original_label = reverse_mapping[pred]
    print(f"🔧 {text}")
    print(f"🏷️ Predicted: {original_label} (confidence: {conf:.4f})")
    print("-" * 50)
```

---

## 🔧 **Method 2: Cell-by-Cell (Alternative)**

If you prefer to run code in separate cells, use the content from `kaggle_notebook_cells.py` - copy each cell section separately.

---

## 💾 **Saving and Downloading Your Model**

### **Automatic Saving**
The script automatically saves your model to `/kaggle/working/` which includes:
- `config.json` - Model configuration
- `pytorch_model.bin` - Model weights  
- `tokenizer.json` - Tokenizer configuration
- `metadata.json` - Training metadata
- `model_data.pkl` - Label mappings

### **Download Files**
1. After training completes, go to the "Output" tab in your Kaggle notebook
2. You'll see all saved files listed
3. Click the download icon next to each file to download
4. Or click "Download All" to get everything as a zip

---

## 🚀 **Expected Results**

### **Training Output:**
```
📁 Found data files: ['/kaggle/input/labor-data/archive_list_inal.xlsx']
📊 Data shape: (199950, 6)
✅ Prepared: 5000 samples, 21 classes
🖥️ Using device: cuda
📖 Epoch 1/2
Training: 100%|██████████| 313/313 [02:15<00:00,  2.31it/s]
📉 Average loss: 2.8456
📖 Epoch 2/2  
Training: 100%|██████████| 313/313 [02:12<00:00,  2.36it/s]
📉 Average loss: 1.9234
Evaluating: 100%|██████████| 63/63 [00:15<00:00,  4.12it/s]
🎯 Test Accuracy: 0.7250
✅ Model saved to: /kaggle/working/bert_labor_classifier
🎉 Training completed! Accuracy: 0.7250
```

### **Model Size:**
- Total size: ~400-500 MB
- Main files:
  - `pytorch_model.bin`: ~440 MB (model weights)
  - `config.json`: ~1 KB (configuration)
  - `tokenizer files`: ~1 MB (tokenizer)
  - `metadata.json`: ~1 KB (your data)

---

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **"CUDA out of memory"**
   - Reduce `batch_size` from 16 to 8 or 4
   - Reduce `MAX_SAMPLES` from 5000 to 2000

2. **"File not found"**
   - Check your data upload in the "Data" tab
   - Verify the file path in the script

3. **"Import errors"**
   - Kaggle usually has all packages pre-installed
   - If needed, add: `!pip install transformers`

4. **"Low accuracy"**
   - Increase `EPOCHS` from 2 to 3-5
   - Increase `MAX_SAMPLES` if you have enough memory
   - Try different learning rates (1e-5, 3e-5)

---

## 🎯 **Next Steps**

1. **Download your trained model**
2. **Use it for inference** with the provided prediction function
3. **Experiment with hyperparameters** for better accuracy
4. **Try different BERT models** (distilbert, roberta, etc.)

Your BERT model is now ready for production use! 🚀
