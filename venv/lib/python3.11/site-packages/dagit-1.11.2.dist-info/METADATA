Metadata-Version: 2.4
Name: dagit
Version: 1.11.2
Summary: Web UI for dagster.
Home-page: https://github.com/dagster-io/dagster
Author: Dagster Labs
Author-email: <EMAIL>
License: Apache-2.0
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.9,<=3.13.3
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: dagster-webserver==1.11.2
Provides-Extra: notebook
Requires-Dist: dagster-webserver[notebook]==1.11.2; extra == "notebook"
Provides-Extra: test
Requires-Dist: dagster-webserver[test]==1.11.2; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

============
Dagster UI
============

Usage
~~~~~
Eg in dagster_examples

.. code-block:: sh

  dagit -p 3333

Running dev ui:

.. code-block:: sh
  NEXT_PUBLIC_BACKEND_ORIGIN="http://localhost:3333" yarn start

