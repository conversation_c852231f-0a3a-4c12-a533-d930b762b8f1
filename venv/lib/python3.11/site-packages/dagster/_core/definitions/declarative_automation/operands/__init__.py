from dagster._core.definitions.declarative_automation.operands.operands import (
    BackfillInProgressAutomationCondition as BackfillInProgressAutomationCondition,
    CheckResultCondition as CheckResultCondition,
    CodeVersionChangedCondition as CodeVersionChangedCondition,
    CronTickPassedCondition as CronTickPassedCondition,
    ExecutionFailedAutomationCondition as ExecutionFailedAutomationCondition,
    InitialEvaluationCondition as InitialEvaluationCondition,
    InLatestTimeWindowCondition as InLatestTimeWindowCondition,
    LatestRunExecutedWithRootTargetCondition as LatestRunExecutedWithRootTargetCondition,
    LatestRunExecutedWithTagsCondition as LatestRunExecutedWithTagsCondition,
    MissingAutomationCondition as MissingAutomationCondition,
    NewlyRequestedCondition as NewlyRequestedCondition,
    NewlyUpdatedCondition as NewlyUpdatedCondition,
    RunInProgressAutomationCondition as RunInProgressAutomationCondition,
    WillBeRequestedCondition as WillBeRequestedCondition,
)
