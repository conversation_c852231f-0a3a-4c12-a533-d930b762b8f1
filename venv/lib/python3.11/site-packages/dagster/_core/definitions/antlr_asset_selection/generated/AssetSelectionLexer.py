# flake8: noqa
# type: ignore
# Generated from AssetSelection.g4 by ANTLR 4.13.2
from antlr4 import *
from io import StringIO
import sys
from typing import <PERSON><PERSON>


def serializedATN():
    return [
        4,
        0,
        29,
        242,
        6,
        -1,
        2,
        0,
        7,
        0,
        2,
        1,
        7,
        1,
        2,
        2,
        7,
        2,
        2,
        3,
        7,
        3,
        2,
        4,
        7,
        4,
        2,
        5,
        7,
        5,
        2,
        6,
        7,
        6,
        2,
        7,
        7,
        7,
        2,
        8,
        7,
        8,
        2,
        9,
        7,
        9,
        2,
        10,
        7,
        10,
        2,
        11,
        7,
        11,
        2,
        12,
        7,
        12,
        2,
        13,
        7,
        13,
        2,
        14,
        7,
        14,
        2,
        15,
        7,
        15,
        2,
        16,
        7,
        16,
        2,
        17,
        7,
        17,
        2,
        18,
        7,
        18,
        2,
        19,
        7,
        19,
        2,
        20,
        7,
        20,
        2,
        21,
        7,
        21,
        2,
        22,
        7,
        22,
        2,
        23,
        7,
        23,
        2,
        24,
        7,
        24,
        2,
        25,
        7,
        25,
        2,
        26,
        7,
        26,
        2,
        27,
        7,
        27,
        2,
        28,
        7,
        28,
        1,
        0,
        1,
        0,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        3,
        1,
        68,
        8,
        1,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        3,
        2,
        74,
        8,
        2,
        1,
        3,
        1,
        3,
        1,
        3,
        1,
        3,
        1,
        3,
        1,
        3,
        3,
        3,
        82,
        8,
        3,
        1,
        4,
        1,
        4,
        1,
        5,
        1,
        5,
        1,
        6,
        4,
        6,
        89,
        8,
        6,
        11,
        6,
        12,
        6,
        90,
        1,
        7,
        1,
        7,
        1,
        8,
        1,
        8,
        1,
        9,
        1,
        9,
        1,
        10,
        1,
        10,
        1,
        11,
        1,
        11,
        1,
        11,
        1,
        11,
        1,
        12,
        1,
        12,
        1,
        12,
        1,
        12,
        1,
        12,
        1,
        12,
        1,
        13,
        1,
        13,
        1,
        13,
        1,
        13,
        1,
        13,
        1,
        13,
        1,
        14,
        1,
        14,
        1,
        14,
        1,
        14,
        1,
        15,
        1,
        15,
        1,
        15,
        1,
        15,
        1,
        15,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        16,
        1,
        17,
        1,
        17,
        1,
        17,
        1,
        17,
        1,
        17,
        1,
        17,
        1,
        17,
        1,
        18,
        1,
        18,
        1,
        18,
        1,
        18,
        1,
        18,
        1,
        18,
        1,
        18,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        19,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        20,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        21,
        1,
        22,
        1,
        22,
        1,
        22,
        1,
        22,
        1,
        22,
        1,
        22,
        1,
        23,
        1,
        23,
        1,
        23,
        1,
        23,
        1,
        23,
        1,
        23,
        1,
        24,
        1,
        24,
        5,
        24,
        208,
        8,
        24,
        10,
        24,
        12,
        24,
        211,
        9,
        24,
        1,
        24,
        1,
        24,
        1,
        25,
        1,
        25,
        5,
        25,
        217,
        8,
        25,
        10,
        25,
        12,
        25,
        220,
        9,
        25,
        1,
        26,
        1,
        26,
        5,
        26,
        224,
        8,
        26,
        10,
        26,
        12,
        26,
        227,
        9,
        26,
        1,
        27,
        1,
        27,
        1,
        27,
        1,
        27,
        1,
        27,
        1,
        27,
        1,
        27,
        1,
        28,
        4,
        28,
        237,
        8,
        28,
        11,
        28,
        12,
        28,
        238,
        1,
        28,
        1,
        28,
        0,
        0,
        29,
        1,
        1,
        3,
        2,
        5,
        3,
        7,
        4,
        9,
        5,
        11,
        6,
        13,
        7,
        15,
        8,
        17,
        9,
        19,
        10,
        21,
        11,
        23,
        12,
        25,
        13,
        27,
        14,
        29,
        15,
        31,
        16,
        33,
        17,
        35,
        18,
        37,
        19,
        39,
        20,
        41,
        21,
        43,
        22,
        45,
        23,
        47,
        24,
        49,
        25,
        51,
        26,
        53,
        27,
        55,
        28,
        57,
        29,
        1,
        0,
        7,
        1,
        0,
        48,
        57,
        4,
        0,
        10,
        10,
        13,
        13,
        34,
        34,
        92,
        92,
        3,
        0,
        65,
        90,
        95,
        95,
        97,
        122,
        4,
        0,
        47,
        57,
        65,
        90,
        95,
        95,
        97,
        122,
        4,
        0,
        42,
        42,
        65,
        90,
        95,
        95,
        97,
        122,
        5,
        0,
        42,
        42,
        47,
        57,
        65,
        90,
        95,
        95,
        97,
        122,
        3,
        0,
        9,
        10,
        13,
        13,
        32,
        32,
        249,
        0,
        1,
        1,
        0,
        0,
        0,
        0,
        3,
        1,
        0,
        0,
        0,
        0,
        5,
        1,
        0,
        0,
        0,
        0,
        7,
        1,
        0,
        0,
        0,
        0,
        9,
        1,
        0,
        0,
        0,
        0,
        11,
        1,
        0,
        0,
        0,
        0,
        13,
        1,
        0,
        0,
        0,
        0,
        15,
        1,
        0,
        0,
        0,
        0,
        17,
        1,
        0,
        0,
        0,
        0,
        19,
        1,
        0,
        0,
        0,
        0,
        21,
        1,
        0,
        0,
        0,
        0,
        23,
        1,
        0,
        0,
        0,
        0,
        25,
        1,
        0,
        0,
        0,
        0,
        27,
        1,
        0,
        0,
        0,
        0,
        29,
        1,
        0,
        0,
        0,
        0,
        31,
        1,
        0,
        0,
        0,
        0,
        33,
        1,
        0,
        0,
        0,
        0,
        35,
        1,
        0,
        0,
        0,
        0,
        37,
        1,
        0,
        0,
        0,
        0,
        39,
        1,
        0,
        0,
        0,
        0,
        41,
        1,
        0,
        0,
        0,
        0,
        43,
        1,
        0,
        0,
        0,
        0,
        45,
        1,
        0,
        0,
        0,
        0,
        47,
        1,
        0,
        0,
        0,
        0,
        49,
        1,
        0,
        0,
        0,
        0,
        51,
        1,
        0,
        0,
        0,
        0,
        53,
        1,
        0,
        0,
        0,
        0,
        55,
        1,
        0,
        0,
        0,
        0,
        57,
        1,
        0,
        0,
        0,
        1,
        59,
        1,
        0,
        0,
        0,
        3,
        67,
        1,
        0,
        0,
        0,
        5,
        73,
        1,
        0,
        0,
        0,
        7,
        81,
        1,
        0,
        0,
        0,
        9,
        83,
        1,
        0,
        0,
        0,
        11,
        85,
        1,
        0,
        0,
        0,
        13,
        88,
        1,
        0,
        0,
        0,
        15,
        92,
        1,
        0,
        0,
        0,
        17,
        94,
        1,
        0,
        0,
        0,
        19,
        96,
        1,
        0,
        0,
        0,
        21,
        98,
        1,
        0,
        0,
        0,
        23,
        100,
        1,
        0,
        0,
        0,
        25,
        104,
        1,
        0,
        0,
        0,
        27,
        110,
        1,
        0,
        0,
        0,
        29,
        116,
        1,
        0,
        0,
        0,
        31,
        120,
        1,
        0,
        0,
        0,
        33,
        125,
        1,
        0,
        0,
        0,
        35,
        139,
        1,
        0,
        0,
        0,
        37,
        146,
        1,
        0,
        0,
        0,
        39,
        153,
        1,
        0,
        0,
        0,
        41,
        164,
        1,
        0,
        0,
        0,
        43,
        175,
        1,
        0,
        0,
        0,
        45,
        193,
        1,
        0,
        0,
        0,
        47,
        199,
        1,
        0,
        0,
        0,
        49,
        205,
        1,
        0,
        0,
        0,
        51,
        214,
        1,
        0,
        0,
        0,
        53,
        221,
        1,
        0,
        0,
        0,
        55,
        228,
        1,
        0,
        0,
        0,
        57,
        236,
        1,
        0,
        0,
        0,
        59,
        60,
        5,
        61,
        0,
        0,
        60,
        2,
        1,
        0,
        0,
        0,
        61,
        62,
        5,
        97,
        0,
        0,
        62,
        63,
        5,
        110,
        0,
        0,
        63,
        68,
        5,
        100,
        0,
        0,
        64,
        65,
        5,
        65,
        0,
        0,
        65,
        66,
        5,
        78,
        0,
        0,
        66,
        68,
        5,
        68,
        0,
        0,
        67,
        61,
        1,
        0,
        0,
        0,
        67,
        64,
        1,
        0,
        0,
        0,
        68,
        4,
        1,
        0,
        0,
        0,
        69,
        70,
        5,
        111,
        0,
        0,
        70,
        74,
        5,
        114,
        0,
        0,
        71,
        72,
        5,
        79,
        0,
        0,
        72,
        74,
        5,
        82,
        0,
        0,
        73,
        69,
        1,
        0,
        0,
        0,
        73,
        71,
        1,
        0,
        0,
        0,
        74,
        6,
        1,
        0,
        0,
        0,
        75,
        76,
        5,
        110,
        0,
        0,
        76,
        77,
        5,
        111,
        0,
        0,
        77,
        82,
        5,
        116,
        0,
        0,
        78,
        79,
        5,
        78,
        0,
        0,
        79,
        80,
        5,
        79,
        0,
        0,
        80,
        82,
        5,
        84,
        0,
        0,
        81,
        75,
        1,
        0,
        0,
        0,
        81,
        78,
        1,
        0,
        0,
        0,
        82,
        8,
        1,
        0,
        0,
        0,
        83,
        84,
        5,
        42,
        0,
        0,
        84,
        10,
        1,
        0,
        0,
        0,
        85,
        86,
        5,
        43,
        0,
        0,
        86,
        12,
        1,
        0,
        0,
        0,
        87,
        89,
        7,
        0,
        0,
        0,
        88,
        87,
        1,
        0,
        0,
        0,
        89,
        90,
        1,
        0,
        0,
        0,
        90,
        88,
        1,
        0,
        0,
        0,
        90,
        91,
        1,
        0,
        0,
        0,
        91,
        14,
        1,
        0,
        0,
        0,
        92,
        93,
        5,
        58,
        0,
        0,
        93,
        16,
        1,
        0,
        0,
        0,
        94,
        95,
        5,
        40,
        0,
        0,
        95,
        18,
        1,
        0,
        0,
        0,
        96,
        97,
        5,
        41,
        0,
        0,
        97,
        20,
        1,
        0,
        0,
        0,
        98,
        99,
        5,
        44,
        0,
        0,
        99,
        22,
        1,
        0,
        0,
        0,
        100,
        101,
        5,
        107,
        0,
        0,
        101,
        102,
        5,
        101,
        0,
        0,
        102,
        103,
        5,
        121,
        0,
        0,
        103,
        24,
        1,
        0,
        0,
        0,
        104,
        105,
        5,
        111,
        0,
        0,
        105,
        106,
        5,
        119,
        0,
        0,
        106,
        107,
        5,
        110,
        0,
        0,
        107,
        108,
        5,
        101,
        0,
        0,
        108,
        109,
        5,
        114,
        0,
        0,
        109,
        26,
        1,
        0,
        0,
        0,
        110,
        111,
        5,
        103,
        0,
        0,
        111,
        112,
        5,
        114,
        0,
        0,
        112,
        113,
        5,
        111,
        0,
        0,
        113,
        114,
        5,
        117,
        0,
        0,
        114,
        115,
        5,
        112,
        0,
        0,
        115,
        28,
        1,
        0,
        0,
        0,
        116,
        117,
        5,
        116,
        0,
        0,
        117,
        118,
        5,
        97,
        0,
        0,
        118,
        119,
        5,
        103,
        0,
        0,
        119,
        30,
        1,
        0,
        0,
        0,
        120,
        121,
        5,
        107,
        0,
        0,
        121,
        122,
        5,
        105,
        0,
        0,
        122,
        123,
        5,
        110,
        0,
        0,
        123,
        124,
        5,
        100,
        0,
        0,
        124,
        32,
        1,
        0,
        0,
        0,
        125,
        126,
        5,
        99,
        0,
        0,
        126,
        127,
        5,
        111,
        0,
        0,
        127,
        128,
        5,
        100,
        0,
        0,
        128,
        129,
        5,
        101,
        0,
        0,
        129,
        130,
        5,
        95,
        0,
        0,
        130,
        131,
        5,
        108,
        0,
        0,
        131,
        132,
        5,
        111,
        0,
        0,
        132,
        133,
        5,
        99,
        0,
        0,
        133,
        134,
        5,
        97,
        0,
        0,
        134,
        135,
        5,
        116,
        0,
        0,
        135,
        136,
        5,
        105,
        0,
        0,
        136,
        137,
        5,
        111,
        0,
        0,
        137,
        138,
        5,
        110,
        0,
        0,
        138,
        34,
        1,
        0,
        0,
        0,
        139,
        140,
        5,
        115,
        0,
        0,
        140,
        141,
        5,
        116,
        0,
        0,
        141,
        142,
        5,
        97,
        0,
        0,
        142,
        143,
        5,
        116,
        0,
        0,
        143,
        144,
        5,
        117,
        0,
        0,
        144,
        145,
        5,
        115,
        0,
        0,
        145,
        36,
        1,
        0,
        0,
        0,
        146,
        147,
        5,
        99,
        0,
        0,
        147,
        148,
        5,
        111,
        0,
        0,
        148,
        149,
        5,
        108,
        0,
        0,
        149,
        150,
        5,
        117,
        0,
        0,
        150,
        151,
        5,
        109,
        0,
        0,
        151,
        152,
        5,
        110,
        0,
        0,
        152,
        38,
        1,
        0,
        0,
        0,
        153,
        154,
        5,
        116,
        0,
        0,
        154,
        155,
        5,
        97,
        0,
        0,
        155,
        156,
        5,
        98,
        0,
        0,
        156,
        157,
        5,
        108,
        0,
        0,
        157,
        158,
        5,
        101,
        0,
        0,
        158,
        159,
        5,
        95,
        0,
        0,
        159,
        160,
        5,
        110,
        0,
        0,
        160,
        161,
        5,
        97,
        0,
        0,
        161,
        162,
        5,
        109,
        0,
        0,
        162,
        163,
        5,
        101,
        0,
        0,
        163,
        40,
        1,
        0,
        0,
        0,
        164,
        165,
        5,
        99,
        0,
        0,
        165,
        166,
        5,
        111,
        0,
        0,
        166,
        167,
        5,
        108,
        0,
        0,
        167,
        168,
        5,
        117,
        0,
        0,
        168,
        169,
        5,
        109,
        0,
        0,
        169,
        170,
        5,
        110,
        0,
        0,
        170,
        171,
        5,
        95,
        0,
        0,
        171,
        172,
        5,
        116,
        0,
        0,
        172,
        173,
        5,
        97,
        0,
        0,
        173,
        174,
        5,
        103,
        0,
        0,
        174,
        42,
        1,
        0,
        0,
        0,
        175,
        176,
        5,
        99,
        0,
        0,
        176,
        177,
        5,
        104,
        0,
        0,
        177,
        178,
        5,
        97,
        0,
        0,
        178,
        179,
        5,
        110,
        0,
        0,
        179,
        180,
        5,
        103,
        0,
        0,
        180,
        181,
        5,
        101,
        0,
        0,
        181,
        182,
        5,
        100,
        0,
        0,
        182,
        183,
        5,
        95,
        0,
        0,
        183,
        184,
        5,
        105,
        0,
        0,
        184,
        185,
        5,
        110,
        0,
        0,
        185,
        186,
        5,
        95,
        0,
        0,
        186,
        187,
        5,
        98,
        0,
        0,
        187,
        188,
        5,
        114,
        0,
        0,
        188,
        189,
        5,
        97,
        0,
        0,
        189,
        190,
        5,
        110,
        0,
        0,
        190,
        191,
        5,
        99,
        0,
        0,
        191,
        192,
        5,
        104,
        0,
        0,
        192,
        44,
        1,
        0,
        0,
        0,
        193,
        194,
        5,
        115,
        0,
        0,
        194,
        195,
        5,
        105,
        0,
        0,
        195,
        196,
        5,
        110,
        0,
        0,
        196,
        197,
        5,
        107,
        0,
        0,
        197,
        198,
        5,
        115,
        0,
        0,
        198,
        46,
        1,
        0,
        0,
        0,
        199,
        200,
        5,
        114,
        0,
        0,
        200,
        201,
        5,
        111,
        0,
        0,
        201,
        202,
        5,
        111,
        0,
        0,
        202,
        203,
        5,
        116,
        0,
        0,
        203,
        204,
        5,
        115,
        0,
        0,
        204,
        48,
        1,
        0,
        0,
        0,
        205,
        209,
        5,
        34,
        0,
        0,
        206,
        208,
        8,
        1,
        0,
        0,
        207,
        206,
        1,
        0,
        0,
        0,
        208,
        211,
        1,
        0,
        0,
        0,
        209,
        207,
        1,
        0,
        0,
        0,
        209,
        210,
        1,
        0,
        0,
        0,
        210,
        212,
        1,
        0,
        0,
        0,
        211,
        209,
        1,
        0,
        0,
        0,
        212,
        213,
        5,
        34,
        0,
        0,
        213,
        50,
        1,
        0,
        0,
        0,
        214,
        218,
        7,
        2,
        0,
        0,
        215,
        217,
        7,
        3,
        0,
        0,
        216,
        215,
        1,
        0,
        0,
        0,
        217,
        220,
        1,
        0,
        0,
        0,
        218,
        216,
        1,
        0,
        0,
        0,
        218,
        219,
        1,
        0,
        0,
        0,
        219,
        52,
        1,
        0,
        0,
        0,
        220,
        218,
        1,
        0,
        0,
        0,
        221,
        225,
        7,
        4,
        0,
        0,
        222,
        224,
        7,
        5,
        0,
        0,
        223,
        222,
        1,
        0,
        0,
        0,
        224,
        227,
        1,
        0,
        0,
        0,
        225,
        223,
        1,
        0,
        0,
        0,
        225,
        226,
        1,
        0,
        0,
        0,
        226,
        54,
        1,
        0,
        0,
        0,
        227,
        225,
        1,
        0,
        0,
        0,
        228,
        229,
        5,
        60,
        0,
        0,
        229,
        230,
        5,
        110,
        0,
        0,
        230,
        231,
        5,
        117,
        0,
        0,
        231,
        232,
        5,
        108,
        0,
        0,
        232,
        233,
        5,
        108,
        0,
        0,
        233,
        234,
        5,
        62,
        0,
        0,
        234,
        56,
        1,
        0,
        0,
        0,
        235,
        237,
        7,
        6,
        0,
        0,
        236,
        235,
        1,
        0,
        0,
        0,
        237,
        238,
        1,
        0,
        0,
        0,
        238,
        236,
        1,
        0,
        0,
        0,
        238,
        239,
        1,
        0,
        0,
        0,
        239,
        240,
        1,
        0,
        0,
        0,
        240,
        241,
        6,
        28,
        0,
        0,
        241,
        58,
        1,
        0,
        0,
        0,
        9,
        0,
        67,
        73,
        81,
        90,
        209,
        218,
        225,
        238,
        1,
        6,
        0,
        0,
    ]


class AssetSelectionLexer(Lexer):
    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [DFA(ds, i) for i, ds in enumerate(atn.decisionToState)]

    EQUAL = 1
    AND = 2
    OR = 3
    NOT = 4
    STAR = 5
    PLUS = 6
    DIGITS = 7
    COLON = 8
    LPAREN = 9
    RPAREN = 10
    COMMA = 11
    KEY = 12
    OWNER = 13
    GROUP = 14
    TAG = 15
    KIND = 16
    CODE_LOCATION = 17
    STATUS = 18
    COLUMN = 19
    TABLE_NAME = 20
    COLUMN_TAG = 21
    CHANGED_IN_BRANCH = 22
    SINKS = 23
    ROOTS = 24
    QUOTED_STRING = 25
    UNQUOTED_STRING = 26
    UNQUOTED_WILDCARD_STRING = 27
    NULL_STRING = 28
    WS = 29

    channelNames = ["DEFAULT_TOKEN_CHANNEL", "HIDDEN"]

    modeNames = ["DEFAULT_MODE"]

    literalNames = [
        "<INVALID>",
        "'='",
        "'*'",
        "'+'",
        "':'",
        "'('",
        "')'",
        "','",
        "'key'",
        "'owner'",
        "'group'",
        "'tag'",
        "'kind'",
        "'code_location'",
        "'status'",
        "'column'",
        "'table_name'",
        "'column_tag'",
        "'changed_in_branch'",
        "'sinks'",
        "'roots'",
        "'<null>'",
    ]

    symbolicNames = [
        "<INVALID>",
        "EQUAL",
        "AND",
        "OR",
        "NOT",
        "STAR",
        "PLUS",
        "DIGITS",
        "COLON",
        "LPAREN",
        "RPAREN",
        "COMMA",
        "KEY",
        "OWNER",
        "GROUP",
        "TAG",
        "KIND",
        "CODE_LOCATION",
        "STATUS",
        "COLUMN",
        "TABLE_NAME",
        "COLUMN_TAG",
        "CHANGED_IN_BRANCH",
        "SINKS",
        "ROOTS",
        "QUOTED_STRING",
        "UNQUOTED_STRING",
        "UNQUOTED_WILDCARD_STRING",
        "NULL_STRING",
        "WS",
    ]

    ruleNames = [
        "EQUAL",
        "AND",
        "OR",
        "NOT",
        "STAR",
        "PLUS",
        "DIGITS",
        "COLON",
        "LPAREN",
        "RPAREN",
        "COMMA",
        "KEY",
        "OWNER",
        "GROUP",
        "TAG",
        "KIND",
        "CODE_LOCATION",
        "STATUS",
        "COLUMN",
        "TABLE_NAME",
        "COLUMN_TAG",
        "CHANGED_IN_BRANCH",
        "SINKS",
        "ROOTS",
        "QUOTED_STRING",
        "UNQUOTED_STRING",
        "UNQUOTED_WILDCARD_STRING",
        "NULL_STRING",
        "WS",
    ]

    grammarFileName = "AssetSelection.g4"

    def __init__(self, input=None, output: TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.2")
        self._interp = LexerATNSimulator(
            self, self.atn, self.decisionsToDFA, PredictionContextCache()
        )
        self._actions = None
        self._predicates = None
