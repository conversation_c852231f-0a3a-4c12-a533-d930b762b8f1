# flake8: noqa
# type: ignore
# Generated from AssetSelection.g4 by ANTLR 4.13.2
# encoding: utf-8
from antlr4 import *
from io import StringIO
import sys
from typing import Text<PERSON>


def serializedATN():
    return [
        4,
        1,
        29,
        120,
        2,
        0,
        7,
        0,
        2,
        1,
        7,
        1,
        2,
        2,
        7,
        2,
        2,
        3,
        7,
        3,
        2,
        4,
        7,
        4,
        2,
        5,
        7,
        5,
        2,
        6,
        7,
        6,
        2,
        7,
        7,
        7,
        2,
        8,
        7,
        8,
        1,
        0,
        1,
        0,
        1,
        0,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        3,
        1,
        37,
        8,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        5,
        1,
        45,
        8,
        1,
        10,
        1,
        12,
        1,
        48,
        9,
        1,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        1,
        2,
        3,
        2,
        60,
        8,
        2,
        1,
        3,
        3,
        3,
        63,
        8,
        3,
        1,
        3,
        1,
        3,
        1,
        4,
        1,
        4,
        3,
        4,
        69,
        8,
        4,
        1,
        5,
        1,
        5,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        3,
        6,
        81,
        8,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        3,
        6,
        106,
        8,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        1,
        6,
        3,
        6,
        114,
        8,
        6,
        1,
        7,
        1,
        7,
        1,
        8,
        1,
        8,
        1,
        8,
        0,
        1,
        2,
        9,
        0,
        2,
        4,
        6,
        8,
        10,
        12,
        14,
        16,
        0,
        3,
        1,
        0,
        23,
        24,
        2,
        0,
        25,
        26,
        28,
        28,
        1,
        0,
        25,
        27,
        133,
        0,
        18,
        1,
        0,
        0,
        0,
        2,
        36,
        1,
        0,
        0,
        0,
        4,
        59,
        1,
        0,
        0,
        0,
        6,
        62,
        1,
        0,
        0,
        0,
        8,
        66,
        1,
        0,
        0,
        0,
        10,
        70,
        1,
        0,
        0,
        0,
        12,
        113,
        1,
        0,
        0,
        0,
        14,
        115,
        1,
        0,
        0,
        0,
        16,
        117,
        1,
        0,
        0,
        0,
        18,
        19,
        3,
        2,
        1,
        0,
        19,
        20,
        5,
        0,
        0,
        1,
        20,
        1,
        1,
        0,
        0,
        0,
        21,
        22,
        6,
        1,
        -1,
        0,
        22,
        37,
        3,
        4,
        2,
        0,
        23,
        24,
        3,
        6,
        3,
        0,
        24,
        25,
        3,
        4,
        2,
        0,
        25,
        26,
        3,
        8,
        4,
        0,
        26,
        37,
        1,
        0,
        0,
        0,
        27,
        28,
        3,
        6,
        3,
        0,
        28,
        29,
        3,
        4,
        2,
        0,
        29,
        37,
        1,
        0,
        0,
        0,
        30,
        31,
        3,
        4,
        2,
        0,
        31,
        32,
        3,
        8,
        4,
        0,
        32,
        37,
        1,
        0,
        0,
        0,
        33,
        34,
        5,
        4,
        0,
        0,
        34,
        37,
        3,
        2,
        1,
        4,
        35,
        37,
        5,
        5,
        0,
        0,
        36,
        21,
        1,
        0,
        0,
        0,
        36,
        23,
        1,
        0,
        0,
        0,
        36,
        27,
        1,
        0,
        0,
        0,
        36,
        30,
        1,
        0,
        0,
        0,
        36,
        33,
        1,
        0,
        0,
        0,
        36,
        35,
        1,
        0,
        0,
        0,
        37,
        46,
        1,
        0,
        0,
        0,
        38,
        39,
        10,
        3,
        0,
        0,
        39,
        40,
        5,
        2,
        0,
        0,
        40,
        45,
        3,
        2,
        1,
        4,
        41,
        42,
        10,
        2,
        0,
        0,
        42,
        43,
        5,
        3,
        0,
        0,
        43,
        45,
        3,
        2,
        1,
        3,
        44,
        38,
        1,
        0,
        0,
        0,
        44,
        41,
        1,
        0,
        0,
        0,
        45,
        48,
        1,
        0,
        0,
        0,
        46,
        44,
        1,
        0,
        0,
        0,
        46,
        47,
        1,
        0,
        0,
        0,
        47,
        3,
        1,
        0,
        0,
        0,
        48,
        46,
        1,
        0,
        0,
        0,
        49,
        60,
        3,
        12,
        6,
        0,
        50,
        51,
        3,
        10,
        5,
        0,
        51,
        52,
        5,
        9,
        0,
        0,
        52,
        53,
        3,
        2,
        1,
        0,
        53,
        54,
        5,
        10,
        0,
        0,
        54,
        60,
        1,
        0,
        0,
        0,
        55,
        56,
        5,
        9,
        0,
        0,
        56,
        57,
        3,
        2,
        1,
        0,
        57,
        58,
        5,
        10,
        0,
        0,
        58,
        60,
        1,
        0,
        0,
        0,
        59,
        49,
        1,
        0,
        0,
        0,
        59,
        50,
        1,
        0,
        0,
        0,
        59,
        55,
        1,
        0,
        0,
        0,
        60,
        5,
        1,
        0,
        0,
        0,
        61,
        63,
        5,
        7,
        0,
        0,
        62,
        61,
        1,
        0,
        0,
        0,
        62,
        63,
        1,
        0,
        0,
        0,
        63,
        64,
        1,
        0,
        0,
        0,
        64,
        65,
        5,
        6,
        0,
        0,
        65,
        7,
        1,
        0,
        0,
        0,
        66,
        68,
        5,
        6,
        0,
        0,
        67,
        69,
        5,
        7,
        0,
        0,
        68,
        67,
        1,
        0,
        0,
        0,
        68,
        69,
        1,
        0,
        0,
        0,
        69,
        9,
        1,
        0,
        0,
        0,
        70,
        71,
        7,
        0,
        0,
        0,
        71,
        11,
        1,
        0,
        0,
        0,
        72,
        73,
        5,
        12,
        0,
        0,
        73,
        74,
        5,
        8,
        0,
        0,
        74,
        114,
        3,
        16,
        8,
        0,
        75,
        76,
        5,
        15,
        0,
        0,
        76,
        77,
        5,
        8,
        0,
        0,
        77,
        80,
        3,
        14,
        7,
        0,
        78,
        79,
        5,
        1,
        0,
        0,
        79,
        81,
        3,
        14,
        7,
        0,
        80,
        78,
        1,
        0,
        0,
        0,
        80,
        81,
        1,
        0,
        0,
        0,
        81,
        114,
        1,
        0,
        0,
        0,
        82,
        83,
        5,
        13,
        0,
        0,
        83,
        84,
        5,
        8,
        0,
        0,
        84,
        114,
        3,
        14,
        7,
        0,
        85,
        86,
        5,
        14,
        0,
        0,
        86,
        87,
        5,
        8,
        0,
        0,
        87,
        114,
        3,
        14,
        7,
        0,
        88,
        89,
        5,
        16,
        0,
        0,
        89,
        90,
        5,
        8,
        0,
        0,
        90,
        114,
        3,
        14,
        7,
        0,
        91,
        92,
        5,
        18,
        0,
        0,
        92,
        93,
        5,
        8,
        0,
        0,
        93,
        114,
        3,
        14,
        7,
        0,
        94,
        95,
        5,
        19,
        0,
        0,
        95,
        96,
        5,
        8,
        0,
        0,
        96,
        114,
        3,
        14,
        7,
        0,
        97,
        98,
        5,
        20,
        0,
        0,
        98,
        99,
        5,
        8,
        0,
        0,
        99,
        114,
        3,
        14,
        7,
        0,
        100,
        101,
        5,
        21,
        0,
        0,
        101,
        102,
        5,
        8,
        0,
        0,
        102,
        105,
        3,
        14,
        7,
        0,
        103,
        104,
        5,
        1,
        0,
        0,
        104,
        106,
        3,
        14,
        7,
        0,
        105,
        103,
        1,
        0,
        0,
        0,
        105,
        106,
        1,
        0,
        0,
        0,
        106,
        114,
        1,
        0,
        0,
        0,
        107,
        108,
        5,
        17,
        0,
        0,
        108,
        109,
        5,
        8,
        0,
        0,
        109,
        114,
        3,
        14,
        7,
        0,
        110,
        111,
        5,
        22,
        0,
        0,
        111,
        112,
        5,
        8,
        0,
        0,
        112,
        114,
        3,
        14,
        7,
        0,
        113,
        72,
        1,
        0,
        0,
        0,
        113,
        75,
        1,
        0,
        0,
        0,
        113,
        82,
        1,
        0,
        0,
        0,
        113,
        85,
        1,
        0,
        0,
        0,
        113,
        88,
        1,
        0,
        0,
        0,
        113,
        91,
        1,
        0,
        0,
        0,
        113,
        94,
        1,
        0,
        0,
        0,
        113,
        97,
        1,
        0,
        0,
        0,
        113,
        100,
        1,
        0,
        0,
        0,
        113,
        107,
        1,
        0,
        0,
        0,
        113,
        110,
        1,
        0,
        0,
        0,
        114,
        13,
        1,
        0,
        0,
        0,
        115,
        116,
        7,
        1,
        0,
        0,
        116,
        15,
        1,
        0,
        0,
        0,
        117,
        118,
        7,
        2,
        0,
        0,
        118,
        17,
        1,
        0,
        0,
        0,
        9,
        36,
        44,
        46,
        59,
        62,
        68,
        80,
        105,
        113,
    ]


class AssetSelectionParser(Parser):
    grammarFileName = "AssetSelection.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [DFA(ds, i) for i, ds in enumerate(atn.decisionToState)]

    sharedContextCache = PredictionContextCache()

    literalNames = [
        "<INVALID>",
        "'='",
        "<INVALID>",
        "<INVALID>",
        "<INVALID>",
        "'*'",
        "'+'",
        "<INVALID>",
        "':'",
        "'('",
        "')'",
        "','",
        "'key'",
        "'owner'",
        "'group'",
        "'tag'",
        "'kind'",
        "'code_location'",
        "'status'",
        "'column'",
        "'table_name'",
        "'column_tag'",
        "'changed_in_branch'",
        "'sinks'",
        "'roots'",
        "<INVALID>",
        "<INVALID>",
        "<INVALID>",
        "'<null>'",
    ]

    symbolicNames = [
        "<INVALID>",
        "EQUAL",
        "AND",
        "OR",
        "NOT",
        "STAR",
        "PLUS",
        "DIGITS",
        "COLON",
        "LPAREN",
        "RPAREN",
        "COMMA",
        "KEY",
        "OWNER",
        "GROUP",
        "TAG",
        "KIND",
        "CODE_LOCATION",
        "STATUS",
        "COLUMN",
        "TABLE_NAME",
        "COLUMN_TAG",
        "CHANGED_IN_BRANCH",
        "SINKS",
        "ROOTS",
        "QUOTED_STRING",
        "UNQUOTED_STRING",
        "UNQUOTED_WILDCARD_STRING",
        "NULL_STRING",
        "WS",
    ]

    RULE_start = 0
    RULE_expr = 1
    RULE_traversalAllowedExpr = 2
    RULE_upTraversal = 3
    RULE_downTraversal = 4
    RULE_functionName = 5
    RULE_attributeExpr = 6
    RULE_value = 7
    RULE_keyValue = 8

    ruleNames = [
        "start",
        "expr",
        "traversalAllowedExpr",
        "upTraversal",
        "downTraversal",
        "functionName",
        "attributeExpr",
        "value",
        "keyValue",
    ]

    EOF = Token.EOF
    EQUAL = 1
    AND = 2
    OR = 3
    NOT = 4
    STAR = 5
    PLUS = 6
    DIGITS = 7
    COLON = 8
    LPAREN = 9
    RPAREN = 10
    COMMA = 11
    KEY = 12
    OWNER = 13
    GROUP = 14
    TAG = 15
    KIND = 16
    CODE_LOCATION = 17
    STATUS = 18
    COLUMN = 19
    TABLE_NAME = 20
    COLUMN_TAG = 21
    CHANGED_IN_BRANCH = 22
    SINKS = 23
    ROOTS = 24
    QUOTED_STRING = 25
    UNQUOTED_STRING = 26
    UNQUOTED_WILDCARD_STRING = 27
    NULL_STRING = 28
    WS = 29

    def __init__(self, input: TokenStream, output: TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.2")
        self._interp = ParserATNSimulator(
            self, self.atn, self.decisionsToDFA, self.sharedContextCache
        )
        self._predicates = None

    class StartContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self):
            return self.getTypedRuleContext(AssetSelectionParser.ExprContext, 0)

        def EOF(self):
            return self.getToken(AssetSelectionParser.EOF, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_start

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterStart"):
                listener.enterStart(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitStart"):
                listener.exitStart(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitStart"):
                return visitor.visitStart(self)
            else:
                return visitor.visitChildren(self)

    def start(self):
        localctx = AssetSelectionParser.StartContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_start)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 18
            self.expr(0)
            self.state = 19
            self.match(AssetSelectionParser.EOF)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class ExprContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_expr

        def copyFrom(self, ctx: ParserRuleContext):
            super().copyFrom(ctx)

    class UpTraversalExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def upTraversal(self):
            return self.getTypedRuleContext(AssetSelectionParser.UpTraversalContext, 0)

        def traversalAllowedExpr(self):
            return self.getTypedRuleContext(AssetSelectionParser.TraversalAllowedExprContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterUpTraversalExpression"):
                listener.enterUpTraversalExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitUpTraversalExpression"):
                listener.exitUpTraversalExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitUpTraversalExpression"):
                return visitor.visitUpTraversalExpression(self)
            else:
                return visitor.visitChildren(self)

    class AndExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i: int = None):
            if i is None:
                return self.getTypedRuleContexts(AssetSelectionParser.ExprContext)
            else:
                return self.getTypedRuleContext(AssetSelectionParser.ExprContext, i)

        def AND(self):
            return self.getToken(AssetSelectionParser.AND, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterAndExpression"):
                listener.enterAndExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitAndExpression"):
                listener.exitAndExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitAndExpression"):
                return visitor.visitAndExpression(self)
            else:
                return visitor.visitChildren(self)

    class AllExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def STAR(self):
            return self.getToken(AssetSelectionParser.STAR, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterAllExpression"):
                listener.enterAllExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitAllExpression"):
                listener.exitAllExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitAllExpression"):
                return visitor.visitAllExpression(self)
            else:
                return visitor.visitChildren(self)

    class TraversalAllowedExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def traversalAllowedExpr(self):
            return self.getTypedRuleContext(AssetSelectionParser.TraversalAllowedExprContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterTraversalAllowedExpression"):
                listener.enterTraversalAllowedExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitTraversalAllowedExpression"):
                listener.exitTraversalAllowedExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitTraversalAllowedExpression"):
                return visitor.visitTraversalAllowedExpression(self)
            else:
                return visitor.visitChildren(self)

    class DownTraversalExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def traversalAllowedExpr(self):
            return self.getTypedRuleContext(AssetSelectionParser.TraversalAllowedExprContext, 0)

        def downTraversal(self):
            return self.getTypedRuleContext(AssetSelectionParser.DownTraversalContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterDownTraversalExpression"):
                listener.enterDownTraversalExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitDownTraversalExpression"):
                listener.exitDownTraversalExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitDownTraversalExpression"):
                return visitor.visitDownTraversalExpression(self)
            else:
                return visitor.visitChildren(self)

    class NotExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def NOT(self):
            return self.getToken(AssetSelectionParser.NOT, 0)

        def expr(self):
            return self.getTypedRuleContext(AssetSelectionParser.ExprContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterNotExpression"):
                listener.enterNotExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitNotExpression"):
                listener.exitNotExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitNotExpression"):
                return visitor.visitNotExpression(self)
            else:
                return visitor.visitChildren(self)

    class OrExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i: int = None):
            if i is None:
                return self.getTypedRuleContexts(AssetSelectionParser.ExprContext)
            else:
                return self.getTypedRuleContext(AssetSelectionParser.ExprContext, i)

        def OR(self):
            return self.getToken(AssetSelectionParser.OR, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterOrExpression"):
                listener.enterOrExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitOrExpression"):
                listener.exitOrExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitOrExpression"):
                return visitor.visitOrExpression(self)
            else:
                return visitor.visitChildren(self)

    class UpAndDownTraversalExpressionContext(ExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def upTraversal(self):
            return self.getTypedRuleContext(AssetSelectionParser.UpTraversalContext, 0)

        def traversalAllowedExpr(self):
            return self.getTypedRuleContext(AssetSelectionParser.TraversalAllowedExprContext, 0)

        def downTraversal(self):
            return self.getTypedRuleContext(AssetSelectionParser.DownTraversalContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterUpAndDownTraversalExpression"):
                listener.enterUpAndDownTraversalExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitUpAndDownTraversalExpression"):
                listener.exitUpAndDownTraversalExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitUpAndDownTraversalExpression"):
                return visitor.visitUpAndDownTraversalExpression(self)
            else:
                return visitor.visitChildren(self)

    def expr(self, _p: int = 0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = AssetSelectionParser.ExprContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 2
        self.enterRecursionRule(localctx, 2, self.RULE_expr, _p)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 36
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input, 0, self._ctx)
            if la_ == 1:
                localctx = AssetSelectionParser.TraversalAllowedExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx

                self.state = 22
                self.traversalAllowedExpr()
                pass

            elif la_ == 2:
                localctx = AssetSelectionParser.UpAndDownTraversalExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 23
                self.upTraversal()
                self.state = 24
                self.traversalAllowedExpr()
                self.state = 25
                self.downTraversal()
                pass

            elif la_ == 3:
                localctx = AssetSelectionParser.UpTraversalExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 27
                self.upTraversal()
                self.state = 28
                self.traversalAllowedExpr()
                pass

            elif la_ == 4:
                localctx = AssetSelectionParser.DownTraversalExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 30
                self.traversalAllowedExpr()
                self.state = 31
                self.downTraversal()
                pass

            elif la_ == 5:
                localctx = AssetSelectionParser.NotExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 33
                self.match(AssetSelectionParser.NOT)
                self.state = 34
                self.expr(4)
                pass

            elif la_ == 6:
                localctx = AssetSelectionParser.AllExpressionContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 35
                self.match(AssetSelectionParser.STAR)
                pass

            self._ctx.stop = self._input.LT(-1)
            self.state = 46
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input, 2, self._ctx)
            while _alt != 2 and _alt != ATN.INVALID_ALT_NUMBER:
                if _alt == 1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    self.state = 44
                    self._errHandler.sync(self)
                    la_ = self._interp.adaptivePredict(self._input, 1, self._ctx)
                    if la_ == 1:
                        localctx = AssetSelectionParser.AndExpressionContext(
                            self, AssetSelectionParser.ExprContext(self, _parentctx, _parentState)
                        )
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 38
                        if not self.precpred(self._ctx, 3):
                            from antlr4.error.Errors import FailedPredicateException

                            raise FailedPredicateException(self, "self.precpred(self._ctx, 3)")
                        self.state = 39
                        self.match(AssetSelectionParser.AND)
                        self.state = 40
                        self.expr(4)
                        pass

                    elif la_ == 2:
                        localctx = AssetSelectionParser.OrExpressionContext(
                            self, AssetSelectionParser.ExprContext(self, _parentctx, _parentState)
                        )
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 41
                        if not self.precpred(self._ctx, 2):
                            from antlr4.error.Errors import FailedPredicateException

                            raise FailedPredicateException(self, "self.precpred(self._ctx, 2)")
                        self.state = 42
                        self.match(AssetSelectionParser.OR)
                        self.state = 43
                        self.expr(3)
                        pass

                self.state = 48
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input, 2, self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx

    class TraversalAllowedExprContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_traversalAllowedExpr

        def copyFrom(self, ctx: ParserRuleContext):
            super().copyFrom(ctx)

    class ParenthesizedExpressionContext(TraversalAllowedExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.TraversalAllowedExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def LPAREN(self):
            return self.getToken(AssetSelectionParser.LPAREN, 0)

        def expr(self):
            return self.getTypedRuleContext(AssetSelectionParser.ExprContext, 0)

        def RPAREN(self):
            return self.getToken(AssetSelectionParser.RPAREN, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterParenthesizedExpression"):
                listener.enterParenthesizedExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitParenthesizedExpression"):
                listener.exitParenthesizedExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitParenthesizedExpression"):
                return visitor.visitParenthesizedExpression(self)
            else:
                return visitor.visitChildren(self)

    class AttributeExpressionContext(TraversalAllowedExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.TraversalAllowedExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def attributeExpr(self):
            return self.getTypedRuleContext(AssetSelectionParser.AttributeExprContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterAttributeExpression"):
                listener.enterAttributeExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitAttributeExpression"):
                listener.exitAttributeExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitAttributeExpression"):
                return visitor.visitAttributeExpression(self)
            else:
                return visitor.visitChildren(self)

    class FunctionCallExpressionContext(TraversalAllowedExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.TraversalAllowedExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def functionName(self):
            return self.getTypedRuleContext(AssetSelectionParser.FunctionNameContext, 0)

        def LPAREN(self):
            return self.getToken(AssetSelectionParser.LPAREN, 0)

        def expr(self):
            return self.getTypedRuleContext(AssetSelectionParser.ExprContext, 0)

        def RPAREN(self):
            return self.getToken(AssetSelectionParser.RPAREN, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterFunctionCallExpression"):
                listener.enterFunctionCallExpression(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitFunctionCallExpression"):
                listener.exitFunctionCallExpression(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitFunctionCallExpression"):
                return visitor.visitFunctionCallExpression(self)
            else:
                return visitor.visitChildren(self)

    def traversalAllowedExpr(self):
        localctx = AssetSelectionParser.TraversalAllowedExprContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_traversalAllowedExpr)
        try:
            self.state = 59
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]:
                localctx = AssetSelectionParser.AttributeExpressionContext(self, localctx)
                self.enterOuterAlt(localctx, 1)
                self.state = 49
                self.attributeExpr()
                pass
            elif token in [23, 24]:
                localctx = AssetSelectionParser.FunctionCallExpressionContext(self, localctx)
                self.enterOuterAlt(localctx, 2)
                self.state = 50
                self.functionName()
                self.state = 51
                self.match(AssetSelectionParser.LPAREN)
                self.state = 52
                self.expr(0)
                self.state = 53
                self.match(AssetSelectionParser.RPAREN)
                pass
            elif token in [9]:
                localctx = AssetSelectionParser.ParenthesizedExpressionContext(self, localctx)
                self.enterOuterAlt(localctx, 3)
                self.state = 55
                self.match(AssetSelectionParser.LPAREN)
                self.state = 56
                self.expr(0)
                self.state = 57
                self.match(AssetSelectionParser.RPAREN)
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class UpTraversalContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def PLUS(self):
            return self.getToken(AssetSelectionParser.PLUS, 0)

        def DIGITS(self):
            return self.getToken(AssetSelectionParser.DIGITS, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_upTraversal

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterUpTraversal"):
                listener.enterUpTraversal(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitUpTraversal"):
                listener.exitUpTraversal(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitUpTraversal"):
                return visitor.visitUpTraversal(self)
            else:
                return visitor.visitChildren(self)

    def upTraversal(self):
        localctx = AssetSelectionParser.UpTraversalContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_upTraversal)
        self._la = 0  # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 62
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la == 7:
                self.state = 61
                self.match(AssetSelectionParser.DIGITS)

            self.state = 64
            self.match(AssetSelectionParser.PLUS)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class DownTraversalContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def PLUS(self):
            return self.getToken(AssetSelectionParser.PLUS, 0)

        def DIGITS(self):
            return self.getToken(AssetSelectionParser.DIGITS, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_downTraversal

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterDownTraversal"):
                listener.enterDownTraversal(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitDownTraversal"):
                listener.exitDownTraversal(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitDownTraversal"):
                return visitor.visitDownTraversal(self)
            else:
                return visitor.visitChildren(self)

    def downTraversal(self):
        localctx = AssetSelectionParser.DownTraversalContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_downTraversal)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 66
            self.match(AssetSelectionParser.PLUS)
            self.state = 68
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input, 5, self._ctx)
            if la_ == 1:
                self.state = 67
                self.match(AssetSelectionParser.DIGITS)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class FunctionNameContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def SINKS(self):
            return self.getToken(AssetSelectionParser.SINKS, 0)

        def ROOTS(self):
            return self.getToken(AssetSelectionParser.ROOTS, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_functionName

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterFunctionName"):
                listener.enterFunctionName(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitFunctionName"):
                listener.exitFunctionName(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitFunctionName"):
                return visitor.visitFunctionName(self)
            else:
                return visitor.visitChildren(self)

    def functionName(self):
        localctx = AssetSelectionParser.FunctionNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_functionName)
        self._la = 0  # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 70
            _la = self._input.LA(1)
            if not (_la == 23 or _la == 24):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class AttributeExprContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_attributeExpr

        def copyFrom(self, ctx: ParserRuleContext):
            super().copyFrom(ctx)

    class GroupAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def GROUP(self):
            return self.getToken(AssetSelectionParser.GROUP, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterGroupAttributeExpr"):
                listener.enterGroupAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitGroupAttributeExpr"):
                listener.exitGroupAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitGroupAttributeExpr"):
                return visitor.visitGroupAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class TagAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def TAG(self):
            return self.getToken(AssetSelectionParser.TAG, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self, i: int = None):
            if i is None:
                return self.getTypedRuleContexts(AssetSelectionParser.ValueContext)
            else:
                return self.getTypedRuleContext(AssetSelectionParser.ValueContext, i)

        def EQUAL(self):
            return self.getToken(AssetSelectionParser.EQUAL, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterTagAttributeExpr"):
                listener.enterTagAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitTagAttributeExpr"):
                listener.exitTagAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitTagAttributeExpr"):
                return visitor.visitTagAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class KeyExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def KEY(self):
            return self.getToken(AssetSelectionParser.KEY, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def keyValue(self):
            return self.getTypedRuleContext(AssetSelectionParser.KeyValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterKeyExpr"):
                listener.enterKeyExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitKeyExpr"):
                listener.exitKeyExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitKeyExpr"):
                return visitor.visitKeyExpr(self)
            else:
                return visitor.visitChildren(self)

    class ColumnAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def COLUMN(self):
            return self.getToken(AssetSelectionParser.COLUMN, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterColumnAttributeExpr"):
                listener.enterColumnAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitColumnAttributeExpr"):
                listener.exitColumnAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitColumnAttributeExpr"):
                return visitor.visitColumnAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class KindAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def KIND(self):
            return self.getToken(AssetSelectionParser.KIND, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterKindAttributeExpr"):
                listener.enterKindAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitKindAttributeExpr"):
                listener.exitKindAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitKindAttributeExpr"):
                return visitor.visitKindAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class CodeLocationAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def CODE_LOCATION(self):
            return self.getToken(AssetSelectionParser.CODE_LOCATION, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterCodeLocationAttributeExpr"):
                listener.enterCodeLocationAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitCodeLocationAttributeExpr"):
                listener.exitCodeLocationAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitCodeLocationAttributeExpr"):
                return visitor.visitCodeLocationAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class OwnerAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def OWNER(self):
            return self.getToken(AssetSelectionParser.OWNER, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterOwnerAttributeExpr"):
                listener.enterOwnerAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitOwnerAttributeExpr"):
                listener.exitOwnerAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitOwnerAttributeExpr"):
                return visitor.visitOwnerAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class ChangedInBranchAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def CHANGED_IN_BRANCH(self):
            return self.getToken(AssetSelectionParser.CHANGED_IN_BRANCH, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterChangedInBranchAttributeExpr"):
                listener.enterChangedInBranchAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitChangedInBranchAttributeExpr"):
                listener.exitChangedInBranchAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitChangedInBranchAttributeExpr"):
                return visitor.visitChangedInBranchAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class ColumnTagAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def COLUMN_TAG(self):
            return self.getToken(AssetSelectionParser.COLUMN_TAG, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self, i: int = None):
            if i is None:
                return self.getTypedRuleContexts(AssetSelectionParser.ValueContext)
            else:
                return self.getTypedRuleContext(AssetSelectionParser.ValueContext, i)

        def EQUAL(self):
            return self.getToken(AssetSelectionParser.EQUAL, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterColumnTagAttributeExpr"):
                listener.enterColumnTagAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitColumnTagAttributeExpr"):
                listener.exitColumnTagAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitColumnTagAttributeExpr"):
                return visitor.visitColumnTagAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class TableNameAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def TABLE_NAME(self):
            return self.getToken(AssetSelectionParser.TABLE_NAME, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterTableNameAttributeExpr"):
                listener.enterTableNameAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitTableNameAttributeExpr"):
                listener.exitTableNameAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitTableNameAttributeExpr"):
                return visitor.visitTableNameAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    class StatusAttributeExprContext(AttributeExprContext):
        def __init__(
            self, parser, ctx: ParserRuleContext
        ):  # actually a AssetSelectionParser.AttributeExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def STATUS(self):
            return self.getToken(AssetSelectionParser.STATUS, 0)

        def COLON(self):
            return self.getToken(AssetSelectionParser.COLON, 0)

        def value(self):
            return self.getTypedRuleContext(AssetSelectionParser.ValueContext, 0)

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterStatusAttributeExpr"):
                listener.enterStatusAttributeExpr(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitStatusAttributeExpr"):
                listener.exitStatusAttributeExpr(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitStatusAttributeExpr"):
                return visitor.visitStatusAttributeExpr(self)
            else:
                return visitor.visitChildren(self)

    def attributeExpr(self):
        localctx = AssetSelectionParser.AttributeExprContext(self, self._ctx, self.state)
        self.enterRule(localctx, 12, self.RULE_attributeExpr)
        try:
            self.state = 113
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [12]:
                localctx = AssetSelectionParser.KeyExprContext(self, localctx)
                self.enterOuterAlt(localctx, 1)
                self.state = 72
                self.match(AssetSelectionParser.KEY)
                self.state = 73
                self.match(AssetSelectionParser.COLON)
                self.state = 74
                self.keyValue()
                pass
            elif token in [15]:
                localctx = AssetSelectionParser.TagAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 2)
                self.state = 75
                self.match(AssetSelectionParser.TAG)
                self.state = 76
                self.match(AssetSelectionParser.COLON)
                self.state = 77
                self.value()
                self.state = 80
                self._errHandler.sync(self)
                la_ = self._interp.adaptivePredict(self._input, 6, self._ctx)
                if la_ == 1:
                    self.state = 78
                    self.match(AssetSelectionParser.EQUAL)
                    self.state = 79
                    self.value()

                pass
            elif token in [13]:
                localctx = AssetSelectionParser.OwnerAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 3)
                self.state = 82
                self.match(AssetSelectionParser.OWNER)
                self.state = 83
                self.match(AssetSelectionParser.COLON)
                self.state = 84
                self.value()
                pass
            elif token in [14]:
                localctx = AssetSelectionParser.GroupAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 4)
                self.state = 85
                self.match(AssetSelectionParser.GROUP)
                self.state = 86
                self.match(AssetSelectionParser.COLON)
                self.state = 87
                self.value()
                pass
            elif token in [16]:
                localctx = AssetSelectionParser.KindAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 5)
                self.state = 88
                self.match(AssetSelectionParser.KIND)
                self.state = 89
                self.match(AssetSelectionParser.COLON)
                self.state = 90
                self.value()
                pass
            elif token in [18]:
                localctx = AssetSelectionParser.StatusAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 6)
                self.state = 91
                self.match(AssetSelectionParser.STATUS)
                self.state = 92
                self.match(AssetSelectionParser.COLON)
                self.state = 93
                self.value()
                pass
            elif token in [19]:
                localctx = AssetSelectionParser.ColumnAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 7)
                self.state = 94
                self.match(AssetSelectionParser.COLUMN)
                self.state = 95
                self.match(AssetSelectionParser.COLON)
                self.state = 96
                self.value()
                pass
            elif token in [20]:
                localctx = AssetSelectionParser.TableNameAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 8)
                self.state = 97
                self.match(AssetSelectionParser.TABLE_NAME)
                self.state = 98
                self.match(AssetSelectionParser.COLON)
                self.state = 99
                self.value()
                pass
            elif token in [21]:
                localctx = AssetSelectionParser.ColumnTagAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 9)
                self.state = 100
                self.match(AssetSelectionParser.COLUMN_TAG)
                self.state = 101
                self.match(AssetSelectionParser.COLON)
                self.state = 102
                self.value()
                self.state = 105
                self._errHandler.sync(self)
                la_ = self._interp.adaptivePredict(self._input, 7, self._ctx)
                if la_ == 1:
                    self.state = 103
                    self.match(AssetSelectionParser.EQUAL)
                    self.state = 104
                    self.value()

                pass
            elif token in [17]:
                localctx = AssetSelectionParser.CodeLocationAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 10)
                self.state = 107
                self.match(AssetSelectionParser.CODE_LOCATION)
                self.state = 108
                self.match(AssetSelectionParser.COLON)
                self.state = 109
                self.value()
                pass
            elif token in [22]:
                localctx = AssetSelectionParser.ChangedInBranchAttributeExprContext(self, localctx)
                self.enterOuterAlt(localctx, 11)
                self.state = 110
                self.match(AssetSelectionParser.CHANGED_IN_BRANCH)
                self.state = 111
                self.match(AssetSelectionParser.COLON)
                self.state = 112
                self.value()
                pass
            else:
                raise NoViableAltException(self)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class ValueContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def NULL_STRING(self):
            return self.getToken(AssetSelectionParser.NULL_STRING, 0)

        def QUOTED_STRING(self):
            return self.getToken(AssetSelectionParser.QUOTED_STRING, 0)

        def UNQUOTED_STRING(self):
            return self.getToken(AssetSelectionParser.UNQUOTED_STRING, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_value

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterValue"):
                listener.enterValue(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitValue"):
                listener.exitValue(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitValue"):
                return visitor.visitValue(self)
            else:
                return visitor.visitChildren(self)

    def value(self):
        localctx = AssetSelectionParser.ValueContext(self, self._ctx, self.state)
        self.enterRule(localctx, 14, self.RULE_value)
        self._la = 0  # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 115
            _la = self._input.LA(1)
            if not (((_la) & ~0x3F) == 0 and ((1 << _la) & 369098752) != 0):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    class KeyValueContext(ParserRuleContext):
        __slots__ = "parser"

        def __init__(self, parser, parent: ParserRuleContext = None, invokingState: int = -1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def QUOTED_STRING(self):
            return self.getToken(AssetSelectionParser.QUOTED_STRING, 0)

        def UNQUOTED_STRING(self):
            return self.getToken(AssetSelectionParser.UNQUOTED_STRING, 0)

        def UNQUOTED_WILDCARD_STRING(self):
            return self.getToken(AssetSelectionParser.UNQUOTED_WILDCARD_STRING, 0)

        def getRuleIndex(self):
            return AssetSelectionParser.RULE_keyValue

        def enterRule(self, listener: ParseTreeListener):
            if hasattr(listener, "enterKeyValue"):
                listener.enterKeyValue(self)

        def exitRule(self, listener: ParseTreeListener):
            if hasattr(listener, "exitKeyValue"):
                listener.exitKeyValue(self)

        def accept(self, visitor: ParseTreeVisitor):
            if hasattr(visitor, "visitKeyValue"):
                return visitor.visitKeyValue(self)
            else:
                return visitor.visitChildren(self)

    def keyValue(self):
        localctx = AssetSelectionParser.KeyValueContext(self, self._ctx, self.state)
        self.enterRule(localctx, 16, self.RULE_keyValue)
        self._la = 0  # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 117
            _la = self._input.LA(1)
            if not (((_la) & ~0x3F) == 0 and ((1 << _la) & 234881024) != 0):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx

    def sempred(self, localctx: RuleContext, ruleIndex: int, predIndex: int):
        if self._predicates == None:
            self._predicates = dict()
        self._predicates[1] = self.expr_sempred
        pred = self._predicates.get(ruleIndex, None)
        if pred is None:
            raise Exception("No predicate with index:" + str(ruleIndex))
        else:
            return pred(localctx, predIndex)

    def expr_sempred(self, localctx: ExprContext, predIndex: int):
        if predIndex == 0:
            return self.precpred(self._ctx, 3)

        if predIndex == 1:
            return self.precpred(self._ctx, 2)
