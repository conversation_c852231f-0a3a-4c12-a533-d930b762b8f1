Metadata-Version: 2.4
Name: dagster_shared
Version: 1.11.2
Summary: Shared code between dagster and dagster-dg-core.
Home-page: https://github.com/dagster-io/dagster/tree/master/python_modules/libraries/dagster-shared
Author: Dagster Labs
Author-email: <EMAIL>
License: Apache-2.0
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
License-File: LICENSE
Requires-Dist: PyYAML>=5.1
Requires-Dist: packaging>=20.9
Requires-Dist: pydantic<3.0.0,>=2
Requires-Dist: typing_extensions<5,>=4.11.0
Requires-Dist: tomlkit
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: buildkite-test-collector; extra == "test"
Requires-Dist: flaky; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: summary
