# =============================================================================
# SIMPLE CLASSIFICATION REPORT - Add this to your existing training cell
# =============================================================================
# Replace the evaluation section in your training function with this enhanced version

# Enhanced Evaluation Section (replace lines 211-228 in your current code)
def enhanced_evaluation(model, test_loader, label_mapping, category_mapping, device):
    """Enhanced evaluation with detailed classification report"""
    
    from sklearn.metrics import classification_report, confusion_matrix
    import numpy as np
    
    print(f"\n🔍 DETAILED MODEL EVALUATION")
    print(f"{'='*50}")
    
    model.eval()
    predictions = []
    true_labels = []
    all_probabilities = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            # Get predictions and probabilities
            batch_predictions = torch.argmax(logits, dim=-1)
            batch_probabilities = torch.softmax(logits, dim=-1)
            
            predictions.extend(batch_predictions.cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(batch_probabilities.cpu().numpy())
    
    # Convert to numpy arrays
    y_true = np.array(true_labels)
    y_pred = np.array(predictions)
    y_prob = np.array(all_probabilities)
    
    # Calculate accuracy
    accuracy = accuracy_score(y_true, y_pred)
    print(f"🎯 Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # Create class names for the report
    class_names = []
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    for i in range(len(set(y_true))):
        original_label = reverse_mapping.get(i, i)
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                class_names.append(f"{original_label}-{category_name[:20]}")  # Truncate long names
            except:
                class_names.append(f"Cat_{original_label}")
        else:
            class_names.append(f"Category_{original_label}")
    
    # Generate classification report
    print(f"\n📋 CLASSIFICATION REPORT:")
    print(f"{'='*80}")
    
    report = classification_report(
        y_true, y_pred, 
        target_names=class_names,
        output_dict=True,
        zero_division=0
    )
    
    # Print the report
    report_str = classification_report(
        y_true, y_pred, 
        target_names=class_names,
        zero_division=0
    )
    print(report_str)
    
    # Additional insights
    print(f"\n📊 PERFORMANCE INSIGHTS:")
    print(f"{'='*40}")
    
    # Best performing classes
    class_f1_scores = []
    for i, class_name in enumerate(class_names):
        if str(i) in report:
            f1_score = report[str(i)]['f1-score']
            support = report[str(i)]['support']
            class_f1_scores.append((f1_score, class_name, support))
    
    class_f1_scores.sort(reverse=True)
    
    print(f"🏆 TOP 3 BEST PERFORMING CLASSES (by F1-score):")
    for i, (f1, name, support) in enumerate(class_f1_scores[:3]):
        print(f"  {i+1}. {name}: F1={f1:.3f} (samples: {support})")
    
    print(f"\n⚠️ TOP 3 WORST PERFORMING CLASSES (by F1-score):")
    for i, (f1, name, support) in enumerate(class_f1_scores[-3:]):
        print(f"  {i+1}. {name}: F1={f1:.3f} (samples: {support})")
    
    # Confidence analysis
    max_confidences = np.max(y_prob, axis=1)
    correct_mask = (y_true == y_pred)
    
    avg_conf_correct = np.mean(max_confidences[correct_mask])
    avg_conf_incorrect = np.mean(max_confidences[~correct_mask])
    
    print(f"\n🎯 CONFIDENCE ANALYSIS:")
    print(f"📊 Average confidence (correct): {avg_conf_correct:.3f}")
    print(f"📊 Average confidence (incorrect): {avg_conf_incorrect:.3f}")
    
    # High confidence predictions
    high_conf_mask = max_confidences > 0.8
    high_conf_accuracy = np.mean(correct_mask[high_conf_mask]) if np.sum(high_conf_mask) > 0 else 0
    
    print(f"📊 High confidence (>0.8) predictions: {np.sum(high_conf_mask)}")
    print(f"📊 High confidence accuracy: {high_conf_accuracy:.3f}")
    
    return accuracy, report

# =============================================================================
# MODIFIED TRAINING FUNCTION - Replace your existing train_bert_model function
# =============================================================================

def train_bert_model_with_report(input_data, output_data, num_classes, 
                                batch_size=8, epochs=2, max_samples=5000):
    """Train BERT model with detailed classification report"""
    
    print(f"🚀 Starting BERT training with detailed evaluation...")
    
    # Limit data for Kaggle
    if len(input_data) > max_samples:
        indices = np.random.choice(len(input_data), max_samples, replace=False)
        input_data = [input_data[i] for i in indices]
        output_data = [output_data[i] for i in indices]
        print(f"⚡ Limited to {max_samples} samples")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    # Load model
    model_name = 'bert-base-uncased'
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, num_labels=num_classes
    ).to(device)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=2e-5)
    
    # Training loop
    model.train()
    train_losses = []
    
    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")
        total_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Training")
        
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average loss: {avg_loss:.4f}")
    
    # Enhanced evaluation with detailed report
    accuracy, classification_report_dict = enhanced_evaluation(
        model, test_loader, label_mapping, category_mapping, device
    )
    
    return model, tokenizer, accuracy, train_losses, classification_report_dict

# =============================================================================
# USAGE - Replace your existing training call with this:
# =============================================================================

# Train the model with detailed classification report
model, tokenizer, accuracy, train_losses, class_report = train_bert_model_with_report(
    input_data, output_data, num_classes
)

print(f"\n🎉 TRAINING COMPLETED WITH DETAILED ANALYSIS!")
print(f"📊 Final Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
