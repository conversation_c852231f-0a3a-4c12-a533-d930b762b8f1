import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    BertTokenizer,
    BertForSequenceClassification,
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Force CPU usage to avoid CUDA issues
import os
os.environ["CUDA_VISIBLE_DEVICES"] = ""
torch.cuda.is_available = lambda: False

class LaborDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def load_and_prepare_data(file_path):
    """Load data from Excel file and prepare for training"""
    print("Loading data from Excel file...")
    df = pd.read_excel(file_path, sheet_name="MergedSheet")
    
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Check if required columns exist
    if 'labor_description_str' not in df.columns:
        raise ValueError("Column 'labor_description_str' not found in the data")
    if 'category_num' not in df.columns:
        raise ValueError("Column 'category_num' not found in the data")
    
    # Clean the data
    df = df.dropna(subset=['labor_description_str', 'category_num'])
    
    # Get input and output data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    print(f"Number of samples: {len(input_data)}")
    print(f"Number of unique categories: {len(set(output_data))}")
    print(f"Categories: {sorted(set(output_data))}")
    
    # Display category mapping
    category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
    print("\nCategory Mapping:")
    for _, row in category_mapping.iterrows():
        print(f"  {row['category_num']}: {row['labor_category_label']}")
    
    return input_data, output_data, len(set(output_data)), category_mapping

def train_bert_model(input_data, output_data, num_classes, batch_size=8, epochs=2, learning_rate=2e-5):
    """Train BERT model for labor description classification"""

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Filter out classes with insufficient samples for stratification
    from collections import Counter
    class_counts = Counter(output_data)
    print(f"Class distribution: {dict(class_counts)}")

    # Filter out classes with less than 2 samples
    min_samples = 2
    valid_classes = [cls for cls, count in class_counts.items() if count >= min_samples]

    # Filter data to only include valid classes
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)

    print(f"Filtered data: {len(filtered_input)} samples with {len(valid_classes)} classes")
    print(f"Removed {len(input_data) - len(filtered_input)} samples from classes with < {min_samples} samples")

    # Update num_classes to reflect filtered data
    num_classes = len(valid_classes)

    # Initialize tokenizer and model
    model_name = 'bert-base-uncased'
    tokenizer = BertTokenizer.from_pretrained(model_name)
    model = BertForSequenceClassification.from_pretrained(
        model_name,
        num_labels=num_classes
    ).to(device)

    # Split data (without stratification to avoid issues with small classes)
    X_train, X_test, y_train, y_test = train_test_split(
        filtered_input, filtered_output, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(X_train)}")
    print(f"Testing samples: {len(X_test)}")
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer and scheduler
    optimizer = AdamW(model.parameters(), lr=learning_rate)
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=0,
        num_training_steps=total_steps
    )
    
    # Training loop
    model.train()
    train_losses = []
    
    for epoch in range(epochs):
        print(f"\nEpoch {epoch + 1}/{epochs}")
        total_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': loss.item()})
        
        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"Average training loss: {avg_loss:.4f}")
    
    # Evaluation
    model.eval()
    predictions = []
    true_labels = []
    
    print("\nEvaluating model...")
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    # Calculate metrics
    accuracy = accuracy_score(true_labels, predictions)
    print(f"\nTest Accuracy: {accuracy:.4f}")
    
    # Classification report
    print("\nClassification Report:")
    print(classification_report(true_labels, predictions))
    
    # Save model
    model.save_pretrained('./bert_labor_classifier')
    tokenizer.save_pretrained('./bert_labor_classifier')
    print("\nModel saved to './bert_labor_classifier'")
    
    return model, tokenizer, train_losses, accuracy

def predict_labor_category(text, model, tokenizer, device, category_mapping):
    """Predict labor category for a given text"""
    model.eval()
    
    encoding = tokenizer(
        text,
        truncation=True,
        padding='max_length',
        max_length=128,
        return_tensors='pt'
    )
    
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    
    with torch.no_grad():
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)
        logits = outputs.logits
        predicted_class = torch.argmax(logits, dim=-1).item()
    
    # Get category label
    category_label = category_mapping[category_mapping['category_num'] == predicted_class]['labor_category_label'].iloc[0]
    
    return predicted_class, category_label

if __name__ == "__main__":
    # File path
    file_path = "archive_list_inal.xlsx"
    
    try:
        # Load and prepare data
        input_data, output_data, num_classes, category_mapping = load_and_prepare_data(file_path)
        
        # Train the model
        model, tokenizer, train_losses, accuracy = train_bert_model(
            input_data, output_data, num_classes,
            batch_size=8, epochs=2, learning_rate=2e-5
        )
        
        # Test predictions on sample data
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print("\n" + "="*50)
        print("TESTING PREDICTIONS")
        print("="*50)
        
        # Test with some sample descriptions
        test_descriptions = [
            "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
            "BATTERY TEST AND REPLACEMENT",
            "BRAKE PAD REPLACEMENT",
            "TIRE ROTATION AND BALANCE"
        ]
        
        for desc in test_descriptions:
            pred_num, pred_label = predict_labor_category(desc, model, tokenizer, device, category_mapping)
            print(f"Description: {desc}")
            print(f"Predicted Category: {pred_num} - {pred_label}")
            print("-" * 50)
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
