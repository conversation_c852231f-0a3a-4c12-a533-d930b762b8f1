import pandas as pd
import numpy as np
import os
import warnings

# Suppress warnings
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"
warnings.filterwarnings('ignore')

def load_and_prepare_data(file_path):
    """Load data from Excel file and prepare for training"""
    print("Loading data from Excel file...")
    df = pd.read_excel(file_path, sheet_name="MergedSheet")
    
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Check if required columns exist
    if 'labor_description_str' not in df.columns:
        raise ValueError("Column 'labor_description_str' not found in the data")
    if 'category_num' not in df.columns:
        raise ValueError("Column 'category_num' not found in the data")
    
    # Clean the data
    df = df.dropna(subset=['labor_description_str', 'category_num'])
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    
    # Get input and output data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    print(f"Number of samples: {len(input_data)}")
    print(f"Number of unique categories: {len(set(output_data))}")
    
    # Display category mapping
    category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
    print("\nCategory Mapping:")
    for _, row in category_mapping.iterrows():
        print(f"  {row['category_num']}: {row['labor_category_label']}")
    
    return input_data, output_data, len(set(output_data)), category_mapping

def try_bert_training(input_data, output_data, num_classes, category_mapping):
    """Attempt BERT training with proper error handling"""
    print("\n" + "="*50)
    print("ATTEMPTING BERT TRAINING")
    print("="*50)
    
    try:
        # Try importing required libraries
        import torch
        from transformers import BertTokenizer, BertForSequenceClassification
        from torch.optim import AdamW
        from torch.utils.data import Dataset, DataLoader
        from sklearn.model_selection import train_test_split
        
        print("✓ All BERT dependencies imported successfully")
        
        # Force CPU usage
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        device = torch.device('cpu')
        print(f"✓ Using device: {device}")
        
        # Limit data for demonstration (BERT training is computationally expensive)
        max_samples = 1000
        if len(input_data) > max_samples:
            print(f"✓ Limiting to {max_samples} samples for demonstration")
            indices = np.random.choice(len(input_data), max_samples, replace=False)
            input_data_limited = [input_data[i] for i in indices]
            output_data_limited = [output_data[i] for i in indices]
        else:
            input_data_limited = input_data
            output_data_limited = output_data
        
        # Load BERT model
        model_name = 'bert-base-uncased'
        print(f"✓ Loading {model_name}...")
        
        tokenizer = BertTokenizer.from_pretrained(model_name)
        model = BertForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=num_classes
        ).to(device)
        
        print("✓ BERT model loaded successfully")
        
        # Simple dataset class
        class SimpleDataset(Dataset):
            def __init__(self, texts, labels, tokenizer, max_length=128):
                self.texts = texts
                self.labels = labels
                self.tokenizer = tokenizer
                self.max_length = max_length
            
            def __len__(self):
                return len(self.texts)
            
            def __getitem__(self, idx):
                text = str(self.texts[idx])
                label = self.labels[idx]
                
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                
                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': torch.tensor(label, dtype=torch.long)
                }
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            input_data_limited, output_data_limited, test_size=0.2, random_state=42
        )
        
        print(f"✓ Training samples: {len(X_train)}")
        print(f"✓ Testing samples: {len(X_test)}")
        
        # Create datasets
        train_dataset = SimpleDataset(X_train, y_train, tokenizer)
        test_dataset = SimpleDataset(X_test, y_test, tokenizer)
        
        train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=2, shuffle=False)
        
        # Setup optimizer
        optimizer = AdamW(model.parameters(), lr=1e-5)
        
        # Training (very limited for demonstration)
        model.train()
        print("✓ Starting training (limited demonstration)...")
        
        total_loss = 0
        num_batches = min(5, len(train_loader))  # Very limited training
        
        for i, batch in enumerate(train_loader):
            if i >= num_batches:
                break
                
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            print(f"  Batch {i+1}/{num_batches}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        print(f"✓ Average training loss: {avg_loss:.4f}")
        
        # Simple evaluation
        model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for i, batch in enumerate(test_loader):
                if i >= 3:  # Limit evaluation
                    break
                    
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['labels'].to(device)
                
                outputs = model(input_ids=input_ids, attention_mask=attention_mask)
                predictions = torch.argmax(outputs.logits, dim=-1)
                
                correct += (predictions == labels).sum().item()
                total += labels.size(0)
        
        accuracy = correct / total if total > 0 else 0
        print(f"✓ BERT Demo Accuracy: {accuracy:.4f} (on {total} samples)")
        
        # Test prediction
        test_text = "oil change service"
        encoding = tokenizer(test_text, return_tensors='pt', truncation=True, padding=True)
        
        with torch.no_grad():
            outputs = model(**encoding)
            prediction = torch.argmax(outputs.logits, dim=-1).item()
            
        pred_label = category_mapping[category_mapping['category_num'] == prediction]['labor_category_label'].iloc[0]
        print(f"✓ Test prediction for '{test_text}': {prediction} - {pred_label}")
        
        print("✓ BERT training demonstration completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ BERT training failed: {e}")
        print("Falling back to TF-IDF approach...")
        return False

def tfidf_training(input_data, output_data, num_classes, category_mapping):
    """TF-IDF based training as fallback"""
    print("\n" + "="*50)
    print("TF-IDF TRAINING")
    print("="*50)
    
    from sklearn.model_selection import train_test_split
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score, classification_report
    from collections import Counter
    import pickle
    
    # Filter out classes with too few samples
    class_counts = Counter(output_data)
    valid_classes = [cls for cls, count in class_counts.items() if count >= 2]
    
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)
    
    print(f"✓ Filtered data: {len(filtered_input)} samples with {len(valid_classes)} classes")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        filtered_input, filtered_output, test_size=0.2, random_state=42
    )
    
    # Create TF-IDF features
    vectorizer = TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))
    X_train_tfidf = vectorizer.fit_transform(X_train)
    X_test_tfidf = vectorizer.transform(X_test)
    
    print(f"✓ TF-IDF features: {X_train_tfidf.shape}")
    
    # Train model
    model = LogisticRegression(max_iter=1000, random_state=42)
    model.fit(X_train_tfidf, y_train)
    
    # Evaluate
    y_pred = model.predict(X_test_tfidf)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"✓ TF-IDF Accuracy: {accuracy:.4f}")
    
    # Save model
    model_data = {
        'model': model,
        'vectorizer': vectorizer,
        'category_mapping': category_mapping,
        'model_name': 'TF-IDF + Logistic Regression'
    }
    
    with open('comprehensive_model.pkl', 'wb') as f:
        pickle.dump(model_data, f)
    
    print("✓ Model saved to 'comprehensive_model.pkl'")
    
    # Test prediction
    test_text = "oil change service"
    text_tfidf = vectorizer.transform([test_text])
    prediction = model.predict(text_tfidf)[0]
    pred_label = category_mapping[category_mapping['category_num'] == prediction]['labor_category_label'].iloc[0]
    
    print(f"✓ Test prediction for '{test_text}': {prediction} - {pred_label}")
    
    return True

if __name__ == "__main__":
    file_path = "archive_list_inal.xlsx"
    
    try:
        # Load data
        input_data, output_data, num_classes, category_mapping = load_and_prepare_data(file_path)
        
        # Try BERT first, fallback to TF-IDF
        bert_success = try_bert_training(input_data, output_data, num_classes, category_mapping)
        
        if not bert_success:
            tfidf_success = tfidf_training(input_data, output_data, num_classes, category_mapping)
            
            if tfidf_success:
                print("\n✓ Training completed successfully using TF-IDF approach!")
            else:
                print("\n✗ Both BERT and TF-IDF training failed!")
        else:
            print("\n✓ BERT training demonstration completed!")
            print("Note: For production use, consider the TF-IDF approach for better performance.")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
