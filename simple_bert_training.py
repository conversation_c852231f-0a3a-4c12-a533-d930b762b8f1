import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
import pickle
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data(file_path):
    """Load data from Excel file and prepare for training"""
    print("Loading data from Excel file...")
    df = pd.read_excel(file_path, sheet_name="MergedSheet")
    
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Check if required columns exist
    if 'labor_description_str' not in df.columns:
        raise ValueError("Column 'labor_description_str' not found in the data")
    if 'category_num' not in df.columns:
        raise ValueError("Column 'category_num' not found in the data")
    
    # Clean the data
    df = df.dropna(subset=['labor_description_str', 'category_num'])

    # Convert text to string and clean
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']  # Remove empty strings

    # Get input and output data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    print(f"Number of samples: {len(input_data)}")
    print(f"Number of unique categories: {len(set(output_data))}")
    print(f"Categories: {sorted(set(output_data))}")
    
    # Display category mapping
    category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
    print("\nCategory Mapping:")
    for _, row in category_mapping.iterrows():
        print(f"  {row['category_num']}: {row['labor_category_label']}")
    
    return input_data, output_data, len(set(output_data)), category_mapping

def train_text_classifier(input_data, output_data, num_classes):
    """Train text classifier using TF-IDF and machine learning models"""

    print("Preparing text features using TF-IDF...")

    # Check class distribution and filter out classes with too few samples
    from collections import Counter
    class_counts = Counter(output_data)
    print(f"Class distribution: {dict(class_counts)}")

    # Filter out classes with less than 2 samples
    min_samples = 2
    valid_classes = [cls for cls, count in class_counts.items() if count >= min_samples]

    # Filter data to only include valid classes
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)

    print(f"Filtered data: {len(filtered_input)} samples with {len(valid_classes)} classes")

    # Split data (without stratification to avoid issues with small classes)
    X_train, X_test, y_train, y_test = train_test_split(
        filtered_input, filtered_output, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(X_train)}")
    print(f"Testing samples: {len(X_test)}")
    
    # Create TF-IDF vectorizer
    vectorizer = TfidfVectorizer(
        max_features=5000,
        stop_words='english',
        ngram_range=(1, 2),
        lowercase=True
    )
    
    # Fit and transform training data
    X_train_tfidf = vectorizer.fit_transform(X_train)
    X_test_tfidf = vectorizer.transform(X_test)
    
    print(f"TF-IDF feature shape: {X_train_tfidf.shape}")
    
    # Train Logistic Regression model
    print(f"\nTraining Logistic Regression...")

    model = LogisticRegression(max_iter=1000, random_state=42)

    # Train model
    model.fit(X_train_tfidf, y_train)

    # Make predictions
    y_pred = model.predict(X_test_tfidf)

    # Calculate accuracy
    accuracy = accuracy_score(y_test, y_pred)
    print(f"Logistic Regression Accuracy: {accuracy:.4f}")

    # Print classification report
    print(f"\nLogistic Regression Classification Report:")
    print(classification_report(y_test, y_pred))

    return model, vectorizer, accuracy, "Logistic Regression"

def predict_labor_category(text, model, vectorizer, category_mapping):
    """Predict labor category for a given text"""
    
    # Transform text using the same vectorizer
    text_tfidf = vectorizer.transform([text])
    
    # Make prediction
    predicted_class = model.predict(text_tfidf)[0]
    
    # Get prediction probability
    if hasattr(model, 'predict_proba'):
        probabilities = model.predict_proba(text_tfidf)[0]
        confidence = max(probabilities)
    else:
        confidence = 1.0
    
    # Get category label
    category_label = category_mapping[category_mapping['category_num'] == predicted_class]['labor_category_label'].iloc[0]
    
    return predicted_class, category_label, confidence

def save_model(model, vectorizer, category_mapping, model_name):
    """Save the trained model and components"""
    model_data = {
        'model': model,
        'vectorizer': vectorizer,
        'category_mapping': category_mapping,
        'model_name': model_name
    }
    
    with open('labor_classifier_model.pkl', 'wb') as f:
        pickle.dump(model_data, f)
    
    print("Model saved to 'labor_classifier_model.pkl'")

def load_model():
    """Load the trained model and components"""
    with open('labor_classifier_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    return model_data['model'], model_data['vectorizer'], model_data['category_mapping'], model_data['model_name']

if __name__ == "__main__":
    # File path
    file_path = "archive_list_inal.xlsx"
    
    try:
        # Load and prepare data
        input_data, output_data, num_classes, category_mapping = load_and_prepare_data(file_path)
        
        # Train the model
        model, vectorizer, accuracy, model_name = train_text_classifier(
            input_data, output_data, num_classes
        )
        
        # Save the model
        save_model(model, vectorizer, category_mapping, model_name)
        
        print("\n" + "="*50)
        print("TESTING PREDICTIONS")
        print("="*50)
        
        # Test with some sample descriptions
        test_descriptions = [
            "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
            "BATTERY TEST AND REPLACEMENT",
            "BRAKE PAD REPLACEMENT",
            "TIRE ROTATION AND BALANCE",
            "DIAGNOSIS OF STEERING SYSTEM",
            "WIPER BLADE REPLACEMENT"
        ]
        
        for desc in test_descriptions:
            pred_num, pred_label, confidence = predict_labor_category(desc, model, vectorizer, category_mapping)
            print(f"Description: {desc}")
            print(f"Predicted Category: {pred_num} - {pred_label}")
            print(f"Confidence: {confidence:.4f}")
            print("-" * 50)
            
        print(f"\nFinal Model Performance:")
        print(f"Model Type: {model_name}")
        print(f"Accuracy: {accuracy:.4f}")
        print(f"Number of Categories: {num_classes}")
        print(f"Training Data Size: {len(input_data)}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
