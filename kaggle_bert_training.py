"""
Kaggle-Friendly BERT Training for Labor Description Classification
Compatible with Kaggle notebooks and environments
"""

import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter
from pathlib import Path

# Kaggle-friendly settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Avoid tokenizer warnings in Kaggle

# Check if running in Kaggle
KAGGLE_ENV = os.path.exists('/kaggle')
if KAGGLE_ENV:
    print("🔍 Detected Kaggle environment")
    # Kaggle paths
    INPUT_PATH = "/kaggle/input"
    OUTPUT_PATH = "/kaggle/working"
else:
    print("🔍 Detected local environment")
    # Local paths
    INPUT_PATH = "."
    OUTPUT_PATH = "."

# Import required libraries with error handling
try:
    from torch.utils.data import Dataset, DataLoader
    from transformers import (
        BertTokenizer, 
        BertForSequenceClassification, 
        get_linear_schedule_with_warmup,
        AutoTokenizer,
        AutoModelForSequenceClassification
    )
    from torch.optim import Adam<PERSON>
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from tqdm.auto import tqdm
    
    print("✅ All required libraries imported successfully")
    TRANSFORMERS_AVAILABLE = True
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing required packages...")
    
    if KAGGLE_ENV:
        # In Kaggle, packages are usually pre-installed
        print("Please ensure transformers and torch are available in your Kaggle environment")
    else:
        # Local installation
        import subprocess
        subprocess.run(["pip", "install", "transformers", "torch", "scikit-learn", "tqdm"])
    
    TRANSFORMERS_AVAILABLE = False

class LaborDataset(Dataset):
    """Dataset class for labor descriptions"""
    
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def load_data_kaggle_friendly(file_path=None):
    """Load data with Kaggle-friendly path handling"""
    
    if file_path is None:
        # Try common file names in Kaggle
        possible_files = [
            "archive_list_inal.xlsx",
            "labor_data.xlsx", 
            "data.xlsx"
        ]
        
        for filename in possible_files:
            full_path = os.path.join(INPUT_PATH, filename)
            if os.path.exists(full_path):
                file_path = full_path
                break
        
        if file_path is None:
            # List available files for debugging
            if KAGGLE_ENV:
                print("Available files in /kaggle/input:")
                for root, dirs, files in os.walk("/kaggle/input"):
                    for file in files:
                        print(f"  {os.path.join(root, file)}")
            raise FileNotFoundError("No suitable data file found")
    
    print(f"📁 Loading data from: {file_path}")
    
    # Try different sheet names
    try:
        df = pd.read_excel(file_path, sheet_name="MergedSheet")
    except:
        try:
            df = pd.read_excel(file_path, sheet_name=0)  # First sheet
        except:
            df = pd.read_excel(file_path)  # Default
    
    print(f"📊 Data shape: {df.shape}")
    print(f"📋 Columns: {df.columns.tolist()}")
    
    return df

def prepare_data_for_training(df, min_samples_per_class=2):
    """Prepare and clean data for training"""
    
    # Check required columns
    required_cols = ['labor_description_str', 'category_num']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ Missing columns: {missing_cols}")
        print(f"Available columns: {df.columns.tolist()}")
        raise ValueError(f"Required columns not found: {missing_cols}")
    
    # Clean data
    print("🧹 Cleaning data...")
    df = df.dropna(subset=required_cols)
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    
    # Get data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    print(f"📈 Total samples: {len(input_data)}")
    print(f"🏷️ Unique categories: {len(set(output_data))}")
    
    # Filter classes with insufficient samples
    class_counts = Counter(output_data)
    print(f"📊 Class distribution: {dict(sorted(class_counts.items()))}")
    
    valid_classes = [cls for cls, count in class_counts.items() if count >= min_samples_per_class]
    removed_classes = [cls for cls, count in class_counts.items() if count < min_samples_per_class]
    
    if removed_classes:
        print(f"🗑️ Removing classes with < {min_samples_per_class} samples: {removed_classes}")
    
    # Filter data
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)
    
    # Create label mapping
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(valid_classes))}
    mapped_output = [label_mapping[label] for label in filtered_output]
    
    print(f"✅ Filtered data: {len(filtered_input)} samples, {len(valid_classes)} classes")
    
    # Get category mapping if available
    category_mapping = None
    if 'labor_category_label' in df.columns:
        category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
        print("\n🏷️ Category Mapping:")
        for _, row in category_mapping.iterrows():
            if row['category_num'] in valid_classes:
                print(f"  {row['category_num']} → {label_mapping[row['category_num']]}: {row['labor_category_label']}")
    
    return filtered_input, mapped_output, len(valid_classes), label_mapping, category_mapping

def train_bert_model_kaggle(input_data, output_data, num_classes, 
                           batch_size=8, epochs=2, learning_rate=2e-5, 
                           max_samples=None, model_name='bert-base-uncased'):
    """Train BERT model with Kaggle-friendly settings"""
    
    print(f"\n{'='*60}")
    print("🚀 BERT MODEL TRAINING")
    print(f"{'='*60}")
    
    # Limit data for Kaggle (memory constraints)
    if max_samples and len(input_data) > max_samples:
        print(f"⚡ Limiting to {max_samples} samples for Kaggle compatibility")
        indices = np.random.choice(len(input_data), max_samples, replace=False)
        input_data = [input_data[i] for i in indices]
        output_data = [output_data[i] for i in indices]
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Load model and tokenizer
    print(f"📥 Loading {model_name}...")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=num_classes
        ).to(device)
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None, [], 0
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    print(f"📚 Training samples: {len(X_train)}")
    print(f"🧪 Testing samples: {len(X_test)}")
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=learning_rate)
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer, num_warmup_steps=0, num_training_steps=total_steps
    )
    
    # Training loop
    model.train()
    train_losses = []
    
    print(f"\n🎯 Starting training for {epochs} epoch(s)...")
    
    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")
        total_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average training loss: {avg_loss:.4f}")
    
    # Evaluation
    print("\n🔍 Evaluating model...")
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    # Calculate metrics
    accuracy = accuracy_score(true_labels, predictions)
    print(f"\n🎯 Test Accuracy: {accuracy:.4f}")
    
    # Classification report
    print("\n📊 Classification Report:")
    print(classification_report(true_labels, predictions))
    
    return model, tokenizer, train_losses, accuracy

def save_model_kaggle(model, tokenizer, label_mapping, category_mapping, 
                     train_losses, accuracy, model_name="bert_labor_classifier"):
    """Save model with Kaggle-friendly paths and formats"""
    
    print(f"\n💾 Saving model...")
    
    # Create output directory
    save_dir = os.path.join(OUTPUT_PATH, model_name)
    os.makedirs(save_dir, exist_ok=True)
    
    try:
        # Save model and tokenizer
        model.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)
        print(f"✅ Model saved to: {save_dir}")
        
        # Save metadata
        metadata = {
            'label_mapping': label_mapping,
            'accuracy': accuracy,
            'train_losses': train_losses,
            'num_classes': len(label_mapping),
            'model_type': 'BERT'
        }
        
        # Save as JSON
        with open(os.path.join(save_dir, 'metadata.json'), 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Save category mapping if available
        if category_mapping is not None:
            category_mapping.to_csv(os.path.join(save_dir, 'category_mapping.csv'), index=False)
        
        # Save as pickle for easy loading
        model_data = {
            'label_mapping': label_mapping,
            'category_mapping': category_mapping,
            'accuracy': accuracy,
            'train_losses': train_losses
        }
        
        with open(os.path.join(save_dir, 'model_data.pkl'), 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"✅ Metadata saved")
        
        # List saved files
        print(f"\n📁 Saved files:")
        for file in os.listdir(save_dir):
            file_path = os.path.join(save_dir, file)
            size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  📄 {file} ({size:.1f} MB)")
        
        return save_dir
        
    except Exception as e:
        print(f"❌ Error saving model: {e}")
        return None

def test_model_predictions(model, tokenizer, label_mapping, category_mapping, device):
    """Test model with sample predictions"""
    
    print(f"\n{'='*60}")
    print("🧪 TESTING PREDICTIONS")
    print(f"{'='*60}")
    
    model.eval()
    
    # Reverse label mapping
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    test_descriptions = [
        "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
        "BATTERY TEST AND REPLACEMENT",
        "BRAKE PAD REPLACEMENT", 
        "TIRE ROTATION AND BALANCE",
        "DIAGNOSIS OF ENGINE PROBLEM",
        "WIPER BLADE REPLACEMENT"
    ]
    
    for desc in test_descriptions:
        encoding = tokenizer(
            desc,
            truncation=True,
            padding='max_length',
            max_length=128,
            return_tensors='pt'
        ).to(device)
        
        with torch.no_grad():
            outputs = model(**encoding)
            logits = outputs.logits
            predicted_class = torch.argmax(logits, dim=-1).item()
            confidence = torch.softmax(logits, dim=-1).max().item()
        
        # Map back to original label
        original_label = reverse_mapping[predicted_class]
        
        # Get category name if available
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                category_display = f"{original_label} - {category_name}"
            except:
                category_display = str(original_label)
        else:
            category_display = str(original_label)
        
        print(f"🔧 Description: {desc}")
        print(f"🏷️ Predicted: {category_display}")
        print(f"📊 Confidence: {confidence:.4f}")
        print("-" * 50)

def main():
    """Main function for Kaggle-friendly BERT training"""
    
    print("🚀 KAGGLE-FRIENDLY BERT TRAINING")
    print("=" * 60)
    
    if not TRANSFORMERS_AVAILABLE:
        print("❌ Transformers not available. Please install required packages.")
        return
    
    try:
        # Load data
        df = load_data_kaggle_friendly()
        
        # Prepare data
        input_data, output_data, num_classes, label_mapping, category_mapping = prepare_data_for_training(df)
        
        # Train model (limit samples for Kaggle)
        max_samples = 5000 if KAGGLE_ENV else None
        
        model, tokenizer, train_losses, accuracy = train_bert_model_kaggle(
            input_data, output_data, num_classes,
            batch_size=8 if torch.cuda.is_available() else 4,
            epochs=2,
            learning_rate=2e-5,
            max_samples=max_samples
        )
        
        if model is not None:
            # Save model
            save_dir = save_model_kaggle(
                model, tokenizer, label_mapping, category_mapping,
                train_losses, accuracy
            )
            
            # Test predictions
            device = next(model.parameters()).device
            test_model_predictions(model, tokenizer, label_mapping, category_mapping, device)
            
            print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
            print(f"📊 Final Accuracy: {accuracy:.4f}")
            print(f"💾 Model saved to: {save_dir}")
            
            if KAGGLE_ENV:
                print(f"📁 Files available in /kaggle/working for download")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
