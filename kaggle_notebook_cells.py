# =============================================================================
# KAGGLE NOTEBOOK CELLS - Copy each cell separately into Kaggle
# =============================================================================

# CELL 1: Setup and Imports
# =============================================================================
import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter
from pathlib import Path

# Kaggle-friendly settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Install packages if needed (uncomment if required)
# !pip install transformers torch scikit-learn

from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification, 
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from tqdm.auto import tqdm

print("✅ All libraries imported successfully")

# CELL 2: Dataset Class
# =============================================================================
class LaborDataset(Dataset):
    """Dataset class for labor descriptions"""
    
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

print("✅ Dataset class defined")

# CELL 3: Data Loading and Preparation
# =============================================================================
def load_and_prepare_data():
    """Load and prepare data for training"""
    
    # Find the data file
    data_files = []
    for root, dirs, files in os.walk("/kaggle/input"):
        for file in files:
            if file.endswith(('.xlsx', '.csv')):
                data_files.append(os.path.join(root, file))
    
    print("Available data files:")
    for file in data_files:
        print(f"  📄 {file}")
    
    # Load the Excel file (adjust path as needed)
    file_path = data_files[0]  # Use first Excel file found
    print(f"📁 Loading: {file_path}")
    
    try:
        df = pd.read_excel(file_path, sheet_name="MergedSheet")
    except:
        df = pd.read_excel(file_path)  # Try default sheet
    
    print(f"📊 Data shape: {df.shape}")
    print(f"📋 Columns: {df.columns.tolist()}")
    
    # Clean data
    df = df.dropna(subset=['labor_description_str', 'category_num'])
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    
    # Get data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    # Filter classes with insufficient samples
    class_counts = Counter(output_data)
    valid_classes = [cls for cls, count in class_counts.items() if count >= 2]
    
    # Filter data
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)
    
    # Create label mapping
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(valid_classes))}
    mapped_output = [label_mapping[label] for label in filtered_output]
    
    print(f"✅ Prepared: {len(filtered_input)} samples, {len(valid_classes)} classes")
    
    # Get category mapping
    category_mapping = None
    if 'labor_category_label' in df.columns:
        category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates()
    
    return filtered_input, mapped_output, len(valid_classes), label_mapping, category_mapping

# Load data
input_data, output_data, num_classes, label_mapping, category_mapping = load_and_prepare_data()

# CELL 4: Model Training Function
# =============================================================================
def train_bert_model(input_data, output_data, num_classes, 
                    batch_size=8, epochs=2, max_samples=5000):
    """Train BERT model"""
    
    print(f"🚀 Starting BERT training...")
    
    # Limit data for Kaggle
    if len(input_data) > max_samples:
        indices = np.random.choice(len(input_data), max_samples, replace=False)
        input_data = [input_data[i] for i in indices]
        output_data = [output_data[i] for i in indices]
        print(f"⚡ Limited to {max_samples} samples")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    # Load model
    model_name = 'bert-base-uncased'
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, num_labels=num_classes
    ).to(device)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=2e-5)
    
    # Training loop
    model.train()
    train_losses = []
    
    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")
        total_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Training")
        
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average loss: {avg_loss:.4f}")
    
    # Evaluation
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            predictions.extend(torch.argmax(outputs.logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(true_labels, predictions)
    print(f"\n🎯 Test Accuracy: {accuracy:.4f}")
    
    return model, tokenizer, accuracy, train_losses

# Train the model
model, tokenizer, accuracy, train_losses = train_bert_model(
    input_data, output_data, num_classes
)

# CELL 5: Save Model
# =============================================================================
def save_model_kaggle(model, tokenizer, label_mapping, category_mapping, accuracy):
    """Save model for Kaggle"""
    
    save_dir = "/kaggle/working/bert_labor_classifier"
    os.makedirs(save_dir, exist_ok=True)
    
    # Save model and tokenizer
    model.save_pretrained(save_dir)
    tokenizer.save_pretrained(save_dir)
    
    # Save metadata
    metadata = {
        'label_mapping': label_mapping,
        'accuracy': float(accuracy),
        'num_classes': len(label_mapping),
        'model_type': 'BERT'
    }
    
    with open(f"{save_dir}/metadata.json", 'w') as f:
        json.dump(metadata, f, indent=2)
    
    # Save as pickle
    model_data = {
        'label_mapping': label_mapping,
        'category_mapping': category_mapping,
        'accuracy': accuracy
    }
    
    with open(f"{save_dir}/model_data.pkl", 'wb') as f:
        pickle.dump(model_data, f)
    
    if category_mapping is not None:
        category_mapping.to_csv(f"{save_dir}/category_mapping.csv", index=False)
    
    print(f"✅ Model saved to: {save_dir}")
    
    # List files
    print("\n📁 Saved files:")
    for file in os.listdir(save_dir):
        size = os.path.getsize(f"{save_dir}/{file}") / (1024*1024)
        print(f"  📄 {file} ({size:.1f} MB)")
    
    return save_dir

# Save the model
save_dir = save_model_kaggle(model, tokenizer, label_mapping, category_mapping, accuracy)

# CELL 6: Test Predictions
# =============================================================================
def test_predictions(model, tokenizer, label_mapping, category_mapping):
    """Test model predictions"""
    
    device = next(model.parameters()).device
    model.eval()
    
    # Reverse mapping
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    test_descriptions = [
        "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
        "BATTERY TEST AND REPLACEMENT",
        "BRAKE PAD REPLACEMENT",
        "TIRE ROTATION AND BALANCE"
    ]
    
    print("🧪 Testing predictions:")
    print("=" * 50)
    
    for desc in test_descriptions:
        encoding = tokenizer(
            desc,
            truncation=True,
            padding='max_length',
            max_length=128,
            return_tensors='pt'
        ).to(device)
        
        with torch.no_grad():
            outputs = model(**encoding)
            predicted_class = torch.argmax(outputs.logits, dim=-1).item()
            confidence = torch.softmax(outputs.logits, dim=-1).max().item()
        
        original_label = reverse_mapping[predicted_class]
        
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                category_display = f"{original_label} - {category_name}"
            except:
                category_display = str(original_label)
        else:
            category_display = str(original_label)
        
        print(f"🔧 {desc}")
        print(f"🏷️ Predicted: {category_display}")
        print(f"📊 Confidence: {confidence:.4f}")
        print("-" * 50)

# Test the model
test_predictions(model, tokenizer, label_mapping, category_mapping)

# CELL 7: Create Inference Function
# =============================================================================
def create_inference_function():
    """Create a reusable inference function"""
    
    inference_code = '''
def predict_labor_category(text, model_path="/kaggle/working/bert_labor_classifier"):
    """
    Predict labor category for a given text description
    
    Args:
        text (str): Labor description text
        model_path (str): Path to saved model
    
    Returns:
        dict: Prediction results
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    import json
    import pickle
    
    # Load model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForSequenceClassification.from_pretrained(model_path)
    
    # Load metadata
    with open(f"{model_path}/metadata.json", 'r') as f:
        metadata = json.load(f)
    
    with open(f"{model_path}/model_data.pkl", 'rb') as f:
        model_data = pickle.load(f)
    
    # Prepare input
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    encoding = tokenizer(
        text,
        truncation=True,
        padding='max_length',
        max_length=128,
        return_tensors='pt'
    ).to(device)
    
    # Make prediction
    with torch.no_grad():
        outputs = model(**encoding)
        predicted_class = torch.argmax(outputs.logits, dim=-1).item()
        confidence = torch.softmax(outputs.logits, dim=-1).max().item()
    
    # Map back to original label
    reverse_mapping = {v: k for k, v in metadata['label_mapping'].items()}
    original_label = reverse_mapping[predicted_class]
    
    return {
        'predicted_class': predicted_class,
        'original_label': original_label,
        'confidence': confidence,
        'text': text
    }

# Example usage:
# result = predict_labor_category("oil change service")
# print(f"Prediction: {result}")
'''
    
    # Save inference function
    with open("/kaggle/working/inference_function.py", 'w') as f:
        f.write(inference_code)
    
    print("✅ Inference function saved to /kaggle/working/inference_function.py")
    print("📋 You can use this function to make predictions on new text")

create_inference_function()

print(f"\n🎉 KAGGLE TRAINING COMPLETED!")
print(f"📊 Final Accuracy: {accuracy:.4f}")
print(f"💾 Model saved and ready for download!")
print(f"📁 All files available in /kaggle/working/")
