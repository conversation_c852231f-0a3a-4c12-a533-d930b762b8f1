# =============================================================================
# KAGGLE NOTEBOOK CELLS - Copy each cell separately into Kaggle
# =============================================================================

# CELL 1: Setup and Imports
# =============================================================================
import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter
from pathlib import Path

# Kaggle-friendly settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Install packages if needed (uncomment if required)
# !pip install transformers torch scikit-learn

from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification, 
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from tqdm.auto import tqdm

print("✅ All libraries imported successfully")

# CELL 2: Dataset Class
# =============================================================================
class LaborDataset(Dataset):
    """Dataset class for labor descriptions"""
    
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

print("✅ Dataset class defined")

# CELL 3: Data Loading and Preparation
# =============================================================================
def load_and_prepare_data():
    """Load and prepare data for training"""
    
    # Find the data file
    data_files = []
    for root, dirs, files in os.walk("/kaggle/input"):
        for file in files:
            if file.endswith(('.xlsx', '.csv')):
                data_files.append(os.path.join(root, file))
    
    print("Available data files:")
    for file in data_files:
        print(f"  📄 {file}")
    
    # Load the Excel file (adjust path as needed)
    file_path = data_files[0]  # Use first Excel file found
    print(f"📁 Loading: {file_path}")
    
    try:
        df = pd.read_excel(file_path, sheet_name="MergedSheet")
    except:
        df = pd.read_excel(file_path)  # Try default sheet
    
    print(f"📊 Data shape: {df.shape}")
    print(f"📋 Columns: {df.columns.tolist()}")
    
    # Clean data
    df = df.dropna(subset=['labor_description_str', 'category_num'])
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    
    # Get data
    input_data = df['labor_description_str'].tolist()
    output_data = df['category_num'].tolist()
    
    # Filter classes with insufficient samples
    class_counts = Counter(output_data)
    valid_classes = [cls for cls, count in class_counts.items() if count >= 2]
    
    # Filter data
    filtered_input = []
    filtered_output = []
    for i, label in enumerate(output_data):
        if label in valid_classes:
            filtered_input.append(input_data[i])
            filtered_output.append(label)
    
    # Create label mapping
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(valid_classes))}
    mapped_output = [label_mapping[label] for label in filtered_output]
    
    print(f"✅ Prepared: {len(filtered_input)} samples, {len(valid_classes)} classes")
    
    # Get category mapping
    category_mapping = None
    if 'labor_category_label' in df.columns:
        category_mapping = df[['labor_category_label', 'category_num']].drop_duplicates()
    
    return filtered_input, mapped_output, len(valid_classes), label_mapping, category_mapping

# Load data
input_data, output_data, num_classes, label_mapping, category_mapping = load_and_prepare_data()

# CELL 4: Model Training Function
# =============================================================================
def train_bert_model(input_data, output_data, num_classes,
                    batch_size=4, epochs=2, max_samples=None):
    """
    Train BERT model with 70:30 train/test split

    Args:
        max_samples (int or None): Maximum samples to use. If None, uses all data.
    """
    
    print(f"🚀 Starting BERT training...")
    
    # Limit data for Kaggle
    if max_samples is not None and len(input_data) > max_samples:
        indices = np.random.choice(len(input_data), max_samples, replace=False)
        input_data = [input_data[i] for i in indices]
        output_data = [output_data[i] for i in indices]
        print(f"⚡ Limited to {max_samples} samples")
    else:
        print(f"📊 Using all available data: {len(input_data)} samples")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    # Load model
    model_name = 'bert-base-uncased'
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, num_labels=num_classes
    ).to(device)
    
    # Split data (70% training, 30% testing)
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.3, random_state=42
    )

    print(f"📊 Data split: {len(X_train)} training samples (70%), {len(X_test)} testing samples (30%)")

    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=2e-5)

    # Mixed precision training for memory efficiency
    scaler = torch.amp.GradScaler('cuda')
    print(f"🔧 Using mixed precision training for memory efficiency")

    # Training loop with memory management and checkpointing
    model.train()
    train_losses = []

    # Gradient accumulation for memory efficiency
    accumulation_steps = 8  # Simulate larger effective batch size
    effective_batch_size = batch_size * accumulation_steps
    print(f"🔧 Using gradient accumulation: effective batch size = {effective_batch_size}")

    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")
        total_loss = 0
        optimizer.zero_grad()  # Initialize gradients

        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")

        for batch_idx, batch in enumerate(progress_bar):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            # Mixed precision forward pass
            with torch.amp.autocast('cuda'):
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                # Scale loss for gradient accumulation
                loss = outputs.loss / accumulation_steps

            # Mixed precision backward pass
            scaler.scale(loss).backward()

            # Update weights every accumulation_steps
            if (batch_idx + 1) % accumulation_steps == 0:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()

            total_loss += loss.item() * accumulation_steps

            # Memory management - clear cache periodically
            if batch_idx % 500 == 0 and batch_idx > 0:
                torch.cuda.empty_cache()

            # Progress update
            progress_bar.set_postfix({
                'loss': f'{loss.item() * accumulation_steps:.4f}',
                'mem': f'{torch.cuda.memory_allocated()/1024**3:.1f}GB'
            })

            # Checkpoint every 5000 batches to prevent loss of progress
            if batch_idx % 5000 == 0 and batch_idx > 0:
                checkpoint_path = f"/kaggle/working/checkpoint_epoch{epoch+1}_batch{batch_idx}"
                os.makedirs(checkpoint_path, exist_ok=True)
                model.save_pretrained(checkpoint_path)
                print(f"\n💾 Checkpoint saved at batch {batch_idx}")

        # Final gradient update for the epoch
        if len(train_loader) % accumulation_steps != 0:
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()

        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average loss: {avg_loss:.4f}")

        # Save checkpoint after each epoch
        epoch_checkpoint_path = f"/kaggle/working/checkpoint_epoch{epoch+1}_complete"
        os.makedirs(epoch_checkpoint_path, exist_ok=True)
        model.save_pretrained(epoch_checkpoint_path)
        tokenizer.save_pretrained(epoch_checkpoint_path)
        print(f"✅ Epoch {epoch+1} checkpoint saved")

        # Clear GPU cache after each epoch
        torch.cuda.empty_cache()
        print(f"🧹 GPU cache cleared after epoch {epoch+1}")
    
    # Evaluation
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            predictions.extend(torch.argmax(outputs.logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(true_labels, predictions)
    print(f"\n🎯 Test Accuracy: {accuracy:.4f}")
    
    return model, tokenizer, accuracy, train_losses

# Memory monitoring function
def print_gpu_memory():
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        print(f"🖥️ GPU Memory - Allocated: {allocated:.1f}GB, Reserved: {reserved:.1f}GB")

# Train the model with full dataset
print(f"🚀 Training BERT with full dataset: {len(input_data)} samples")
print(f"📊 Memory-optimized training with gradient accumulation")
print_gpu_memory()
model, tokenizer, accuracy, train_losses = train_bert_model(
    input_data, output_data, num_classes,
    batch_size=2,  # Smaller batch size for memory efficiency
    epochs=2,      # Keep 2 epochs
    max_samples=None  # Use all samples
)

# CELL 5: Save Model
# =============================================================================
def save_model_kaggle(model, tokenizer, label_mapping, category_mapping, accuracy):
    """Save model for Kaggle"""
    
    save_dir = "/kaggle/working/bert_labor_classifier"
    os.makedirs(save_dir, exist_ok=True)
    
    # Save model and tokenizer
    model.save_pretrained(save_dir)
    tokenizer.save_pretrained(save_dir)
    
    # Save metadata
    metadata = {
        'label_mapping': label_mapping,
        'accuracy': float(accuracy),
        'num_classes': len(label_mapping),
        'model_type': 'BERT'
    }
    
    with open(f"{save_dir}/metadata.json", 'w') as f:
        json.dump(metadata, f, indent=2)
    
    # Save as pickle
    model_data = {
        'label_mapping': label_mapping,
        'category_mapping': category_mapping,
        'accuracy': accuracy
    }
    
    with open(f"{save_dir}/model_data.pkl", 'wb') as f:
        pickle.dump(model_data, f)
    
    if category_mapping is not None:
        category_mapping.to_csv(f"{save_dir}/category_mapping.csv", index=False)
    
    print(f"✅ Model saved to: {save_dir}")
    
    # List files
    print("\n📁 Saved files:")
    for file in os.listdir(save_dir):
        size = os.path.getsize(f"{save_dir}/{file}") / (1024*1024)
        print(f"  📄 {file} ({size:.1f} MB)")
    
    return save_dir

# Save the model
save_dir = save_model_kaggle(model, tokenizer, label_mapping, category_mapping, accuracy)

# CELL 6: Test Predictions
# =============================================================================
def test_predictions(model, tokenizer, label_mapping, category_mapping):
    """Test model predictions"""
    
    device = next(model.parameters()).device
    model.eval()
    
    # Reverse mapping
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    test_descriptions = [
        "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
        "BATTERY TEST AND REPLACEMENT",
        "BRAKE PAD REPLACEMENT",
        "TIRE ROTATION AND BALANCE"
    ]
    
    print("🧪 Testing predictions:")
    print("=" * 50)
    
    for desc in test_descriptions:
        encoding = tokenizer(
            desc,
            truncation=True,
            padding='max_length',
            max_length=128,
            return_tensors='pt'
        ).to(device)
        
        with torch.no_grad():
            outputs = model(**encoding)
            predicted_class = torch.argmax(outputs.logits, dim=-1).item()
            confidence = torch.softmax(outputs.logits, dim=-1).max().item()
        
        original_label = reverse_mapping[predicted_class]
        
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                category_display = f"{original_label} - {category_name}"
            except:
                category_display = str(original_label)
        else:
            category_display = str(original_label)
        
        print(f"🔧 {desc}")
        print(f"🏷️ Predicted: {category_display}")
        print(f"📊 Confidence: {confidence:.4f}")
        print("-" * 50)

# Test the model
test_predictions(model, tokenizer, label_mapping, category_mapping)

# CELL 7: Create Inference Function
# =============================================================================
def create_inference_function():
    """Create a reusable inference function"""
    
    inference_code = '''
def predict_labor_category(text, model_path="/kaggle/working/bert_labor_classifier"):
    """
    Predict labor category for a given text description
    
    Args:
        text (str): Labor description text
        model_path (str): Path to saved model
    
    Returns:
        dict: Prediction results
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    import json
    import pickle
    
    # Load model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForSequenceClassification.from_pretrained(model_path)
    
    # Load metadata
    with open(f"{model_path}/metadata.json", 'r') as f:
        metadata = json.load(f)
    
    with open(f"{model_path}/model_data.pkl", 'rb') as f:
        model_data = pickle.load(f)
    
    # Prepare input
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    encoding = tokenizer(
        text,
        truncation=True,
        padding='max_length',
        max_length=128,
        return_tensors='pt'
    ).to(device)
    
    # Make prediction
    with torch.no_grad():
        outputs = model(**encoding)
        predicted_class = torch.argmax(outputs.logits, dim=-1).item()
        confidence = torch.softmax(outputs.logits, dim=-1).max().item()
    
    # Map back to original label
    reverse_mapping = {v: k for k, v in metadata['label_mapping'].items()}
    original_label = reverse_mapping[predicted_class]
    
    return {
        'predicted_class': predicted_class,
        'original_label': original_label,
        'confidence': confidence,
        'text': text
    }

# Example usage:
# result = predict_labor_category("oil change service")
# print(f"Prediction: {result}")
'''
    
    # Save inference function
    with open("/kaggle/working/inference_function.py", 'w') as f:
        f.write(inference_code)
    
    print("✅ Inference function saved to /kaggle/working/inference_function.py")
    print("📋 You can use this function to make predictions on new text")

create_inference_function()

print(f"\n🎉 KAGGLE TRAINING COMPLETED!")
print(f"📊 Final Accuracy: {accuracy:.4f}")
print(f"💾 Model saved and ready for download!")
print(f"📁 All files available in /kaggle/working/")


# CELL 8: Generate Classification Report
# =============================================================================
def generate_classification_report_with_f1():
    """Generate comprehensive classification report with F1 scores"""
    
    from sklearn.metrics import classification_report
    import numpy as np
    from datetime import datetime
    
    print(f"\n📊 COMPREHENSIVE CLASSIFICATION REPORT WITH F1 SCORES")
    print("="*70)
    
    # Re-evaluate the model to get predictions for classification report
    device = next(model.parameters()).device
    model.eval()
    
    # Split data again (same as training) to get test set
    _, X_test, _, y_test = train_test_split(
        input_data, output_data, test_size=0.3, random_state=42
    )
    
    # Create test dataset
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)
    
    # Get predictions and confidences
    predictions = []
    true_labels = []
    confidences = []
    
    print("🔍 Generating detailed predictions...")
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating for report"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            
            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
            confidences.extend(torch.max(probs, dim=-1)[0].cpu().numpy())
    
    # Create class names for the classes that appear in test set
    unique_labels = sorted(set(true_labels + predictions))
    print(f"🔍 Classes present in test set: {len(unique_labels)} out of {num_classes}")
    
    class_names = []
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    for label in unique_labels:
        original_label = reverse_mapping.get(label, label)
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                class_names.append(f"{original_label}-{category_name[:25]}")
            except:
                class_names.append(f"Cat_{original_label}")
        else:
            class_names.append(f"Category_{original_label}")
    
    # Generate classification report
    accuracy = accuracy_score(true_labels, predictions)
    print(f"🎯 Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    print(f"\n📋 DETAILED METRICS BY CLASS:")
    print("="*80)
    
    # Generate report with proper labels
    report = classification_report(
        true_labels, predictions, 
        labels=unique_labels,
        target_names=class_names,
        output_dict=True,
        zero_division=0
    )
    
    # Print formatted report
    report_str = classification_report(
        true_labels, predictions, 
        labels=unique_labels,
        target_names=class_names,
        zero_division=0
    )
    print(report_str)
    
    # Performance insights with F1 scores
    print(f"\n📊 PERFORMANCE INSIGHTS:")
    print("="*40)
    
    # Best and worst classes by F1 score
    class_f1_scores = []
    for i, label in enumerate(unique_labels):
        class_name = class_names[i]
        if str(label) in report:
            f1_score = report[str(label)]['f1-score']
            support = report[str(label)]['support']
            precision = report[str(label)]['precision']
            recall = report[str(label)]['recall']
            class_f1_scores.append((f1_score, class_name, support, precision, recall))
    
    class_f1_scores.sort(reverse=True)
    
    print(f"🏆 TOP 5 BEST PERFORMING CLASSES (by F1-Score):")
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[:5]):
        print(f"  {i+1}. {name}")
        print(f"     F1-Score: {f1:.4f} | Precision: {prec:.3f} | Recall: {rec:.3f} | Samples: {support}")
    
    print(f"\n⚠️ TOP 5 WORST PERFORMING CLASSES (by F1-Score):")
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[-5:]):
        print(f"  {i+1}. {name}")
        print(f"     F1-Score: {f1:.4f} | Precision: {prec:.3f} | Recall: {rec:.3f} | Samples: {support}")
    
    # Overall F1 scores
    weighted_f1 = report['weighted avg']['f1-score']
    macro_f1 = report['macro avg']['f1-score']
    
    print(f"\n📊 OVERALL F1 SCORES:")
    print(f"🎯 Weighted Average F1-Score: {weighted_f1:.4f}")
    print(f"🎯 Macro Average F1-Score: {macro_f1:.4f}")
    
    # Confidence analysis
    correct_mask = np.array(true_labels) == np.array(predictions)
    avg_conf_correct = np.mean(np.array(confidences)[correct_mask])
    avg_conf_incorrect = np.mean(np.array(confidences)[~correct_mask])
    
    print(f"\n🎯 CONFIDENCE ANALYSIS:")
    print(f"📊 Average confidence (correct predictions): {avg_conf_correct:.3f}")
    print(f"📊 Average confidence (incorrect predictions): {avg_conf_incorrect:.3f}")
    print(f"📊 Confidence gap: {avg_conf_correct - avg_conf_incorrect:.3f}")
    
    # High confidence analysis
    high_conf_mask = np.array(confidences) > 0.8
    high_conf_accuracy = np.mean(correct_mask[high_conf_mask]) if np.sum(high_conf_mask) > 0 else 0
    
    print(f"📊 High confidence (>0.8) predictions: {np.sum(high_conf_mask)}")
    print(f"📊 High confidence accuracy: {high_conf_accuracy:.3f}")
    
    # Save classification report to Kaggle working directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"\n💾 SAVING CLASSIFICATION REPORT...")
    print("="*40)
    
    # Save detailed text summary with F1 scores
    text_summary = f"""
BERT Labor Classification Report
Generated: {timestamp}
================================

OVERALL PERFORMANCE METRICS:
- Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)
- Weighted Avg F1-Score: {weighted_f1:.4f}
- Macro Avg F1-Score: {macro_f1:.4f}
- Total Test Samples: {len(true_labels)}
- Correct Predictions: {sum(correct_mask)}

DETAILED CLASS PERFORMANCE:
===========================
"""
    
    # Add all classes with F1 scores
    for i, label in enumerate(unique_labels):
        class_name = class_names[i]
        if str(label) in report:
            f1_score = report[str(label)]['f1-score']
            precision = report[str(label)]['precision']
            recall = report[str(label)]['recall']
            support = report[str(label)]['support']
            
            text_summary += f"{class_name}:\n"
            text_summary += f"  - Precision: {precision:.4f}\n"
            text_summary += f"  - Recall: {recall:.4f}\n"
            text_summary += f"  - F1-Score: {f1_score:.4f}\n"
            text_summary += f"  - Support: {support}\n\n"
    
    text_summary += f"\nTOP 5 BEST PERFORMING CLASSES (by F1-Score):\n"
    text_summary += "=" * 50 + "\n"
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[:5]):
        text_summary += f"{i+1}. {name}\n"
        text_summary += f"   F1-Score: {f1:.4f} | Precision: {prec:.4f} | Recall: {rec:.4f} | Support: {support}\n\n"
    
    text_summary += f"\nTOP 5 WORST PERFORMING CLASSES (by F1-Score):\n"
    text_summary += "=" * 50 + "\n"
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[-5:]):
        text_summary += f"{i+1}. {name}\n"
        text_summary += f"   F1-Score: {f1:.4f} | Precision: {prec:.4f} | Recall: {rec:.4f} | Support: {support}\n\n"
    
    # Add confidence analysis
    text_summary += f"\nCONFIDENCE ANALYSIS:\n"
    text_summary += "=" * 20 + "\n"
    text_summary += f"Average confidence (correct predictions): {avg_conf_correct:.4f}\n"
    text_summary += f"Average confidence (incorrect predictions): {avg_conf_incorrect:.4f}\n"
    text_summary += f"Confidence gap: {avg_conf_correct - avg_conf_incorrect:.4f}\n"
    text_summary += f"High confidence (>0.8) predictions: {np.sum(high_conf_mask)}\n"
    text_summary += f"High confidence accuracy: {high_conf_accuracy:.4f}\n"
    
    # Save to file
    text_path = f"/kaggle/working/classification_summary_{timestamp}.txt"
    with open(text_path, 'w') as f:
        f.write(text_summary)
    print(f"✅ Classification summary saved: {text_path}")
    
    # Save CSV with F1 scores
    import pandas as pd
    csv_data = []
    for i, label in enumerate(unique_labels):
        class_name = class_names[i]
        if str(label) in report:
            csv_data.append({
                'Class_Name': class_name,
                'Precision': report[str(label)]['precision'],
                'Recall': report[str(label)]['recall'],
                'F1_Score': report[str(label)]['f1-score'],
                'Support': report[str(label)]['support']
            })
    
    df_report = pd.DataFrame(csv_data)
    csv_path = f"/kaggle/working/classification_metrics_{timestamp}.csv"
    df_report.to_csv(csv_path, index=False)
    print(f"✅ CSV metrics saved: {csv_path}")
    
    # Save F1 ranking
    f1_ranking_data = []
    for rank, (f1, name, support, prec, rec) in enumerate(class_f1_scores, 1):
        f1_ranking_data.append({
            'Rank': rank,
            'Class_Name': name,
            'F1_Score': f1,
            'Precision': prec,
            'Recall': rec,
            'Support': support
        })
    
    f1_df = pd.DataFrame(f1_ranking_data)
    f1_path = f"/kaggle/working/f1_score_ranking_{timestamp}.csv"
    f1_df.to_csv(f1_path, index=False)
    print(f"✅ F1 Score ranking saved: {f1_path}")
    
    print(f"\n📁 ALL CLASSIFICATION REPORTS SAVED TO: /kaggle/working/")
    print(f"📥 Available for download in Kaggle Output tab")
    
    return accuracy, report

# Generate the classification report
final_accuracy, detailed_report = generate_classification_report_with_f1()

print(f"\n🎉 CLASSIFICATION REPORT GENERATED!")
print(f"📊 Final Accuracy: {final_accuracy:.4f}")
print(f"📊 Weighted F1-Score: {detailed_report['weighted avg']['f1-score']:.4f}")
print(f"📊 Macro F1-Score: {detailed_report['macro avg']['f1-score']:.4f}")