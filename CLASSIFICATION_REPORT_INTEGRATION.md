# 📊 Adding Classification Report to Your BERT Training

## 🎯 Quick Integration Options

You have **3 ways** to add a comprehensive classification report to your existing code:

---

## **Option 1: Quick Addition (Easiest)** ⚡

Simply add this code **after line 227** in your existing training function:

```python
# Add this after: accuracy = accuracy_score(true_labels, predictions)

from sklearn.metrics import classification_report

# Create class names
class_names = []
reverse_mapping = {v: k for k, v in label_mapping.items()}

for i in range(num_classes):
    original_label = reverse_mapping.get(i, i)
    if category_mapping is not None:
        try:
            category_name = category_mapping[
                category_mapping['category_num'] == original_label
            ]['labor_category_label'].iloc[0]
            class_names.append(f"{original_label}-{category_name[:20]}")
        except:
            class_names.append(f"Cat_{original_label}")
    else:
        class_names.append(f"Category_{original_label}")

# Generate and print classification report
print(f"\n📋 DETAILED CLASSIFICATION REPORT:")
print("="*60)

report = classification_report(
    true_labels, predictions, 
    target_names=class_names,
    zero_division=0
)
print(report)

# Additional insights
from sklearn.metrics import precision_recall_fscore_support
precision, recall, f1, support = precision_recall_fscore_support(
    true_labels, predictions, average='weighted'
)

print(f"\n📊 WEIGHTED AVERAGES:")
print(f"🎯 Precision: {precision:.4f}")
print(f"🎯 Recall:    {recall:.4f}")
print(f"🎯 F1-Score:  {f1:.4f}")
```

---

## **Option 2: Add New Cell (Recommended)** ⭐

Add this as a **new cell** after your training cell:

```python
# CELL 4B: Detailed Classification Report
# =============================================================================

def generate_classification_report(model, tokenizer, input_data, output_data, 
                                 label_mapping, category_mapping, num_classes):
    """Generate comprehensive classification report"""
    
    from sklearn.metrics import classification_report, confusion_matrix
    import numpy as np
    
    print(f"\n📊 COMPREHENSIVE CLASSIFICATION REPORT")
    print("="*60)
    
    # Prepare test data
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    X_train, X_test, y_train, y_test = train_test_split(
        input_data, output_data, test_size=0.2, random_state=42
    )
    
    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    # Get predictions
    predictions = []
    true_labels = []
    confidences = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            
            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
            confidences.extend(torch.max(probs, dim=-1)[0].cpu().numpy())
    
    # Create class names
    class_names = []
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    for i in range(num_classes):
        original_label = reverse_mapping.get(i, i)
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                class_names.append(f"{original_label}-{category_name[:25]}")
            except:
                class_names.append(f"Cat_{original_label}")
        else:
            class_names.append(f"Category_{original_label}")
    
    # Generate classification report
    accuracy = accuracy_score(true_labels, predictions)
    print(f"🎯 Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    print(f"\n📋 DETAILED METRICS BY CLASS:")
    print("="*80)
    
    report = classification_report(
        true_labels, predictions, 
        target_names=class_names,
        output_dict=True,
        zero_division=0
    )
    
    # Print formatted report
    report_str = classification_report(
        true_labels, predictions, 
        target_names=class_names,
        zero_division=0
    )
    print(report_str)
    
    # Performance insights
    print(f"\n📊 PERFORMANCE INSIGHTS:")
    print("="*40)
    
    # Best and worst classes
    class_f1_scores = []
    for i, class_name in enumerate(class_names):
        if str(i) in report:
            f1_score = report[str(i)]['f1-score']
            support = report[str(i)]['support']
            precision = report[str(i)]['precision']
            recall = report[str(i)]['recall']
            class_f1_scores.append((f1_score, class_name, support, precision, recall))
    
    class_f1_scores.sort(reverse=True)
    
    print(f"🏆 TOP 3 BEST PERFORMING CLASSES:")
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[:3]):
        print(f"  {i+1}. {name}")
        print(f"     F1: {f1:.3f} | Precision: {prec:.3f} | Recall: {rec:.3f} | Samples: {support}")
    
    print(f"\n⚠️ TOP 3 WORST PERFORMING CLASSES:")
    for i, (f1, name, support, prec, rec) in enumerate(class_f1_scores[-3:]):
        print(f"  {i+1}. {name}")
        print(f"     F1: {f1:.3f} | Precision: {prec:.3f} | Recall: {rec:.3f} | Samples: {support}")
    
    # Confidence analysis
    correct_mask = np.array(true_labels) == np.array(predictions)
    avg_conf_correct = np.mean(np.array(confidences)[correct_mask])
    avg_conf_incorrect = np.mean(np.array(confidences)[~correct_mask])
    
    print(f"\n🎯 CONFIDENCE ANALYSIS:")
    print(f"📊 Average confidence (correct predictions): {avg_conf_correct:.3f}")
    print(f"📊 Average confidence (incorrect predictions): {avg_conf_incorrect:.3f}")
    print(f"📊 Confidence gap: {avg_conf_correct - avg_conf_incorrect:.3f}")
    
    # High confidence analysis
    high_conf_mask = np.array(confidences) > 0.8
    high_conf_accuracy = np.mean(correct_mask[high_conf_mask]) if np.sum(high_conf_mask) > 0 else 0
    
    print(f"📊 High confidence (>0.8) predictions: {np.sum(high_conf_mask)}")
    print(f"📊 High confidence accuracy: {high_conf_accuracy:.3f}")
    
    return accuracy, report

# Generate the classification report
final_accuracy, detailed_report = generate_classification_report(
    model, tokenizer, input_data, output_data, 
    label_mapping, category_mapping, num_classes
)

print(f"\n🎉 CLASSIFICATION ANALYSIS COMPLETE!")
print(f"📊 Final Accuracy: {final_accuracy:.4f}")
```

---

## **Option 3: Complete Replacement** 🔄

Replace your entire training function with the enhanced version from `simple_classification_report.py`.

---

## 📊 **What You'll Get**

### **Basic Metrics:**
- Overall accuracy, precision, recall, F1-score
- Per-class precision, recall, F1-score, support

### **Performance Insights:**
- Best and worst performing classes
- Classes with insufficient training data
- Confidence analysis for predictions

### **Sample Output:**
```
📊 COMPREHENSIVE CLASSIFICATION REPORT
============================================================
🎯 Overall Accuracy: 0.7250 (72.50%)

📋 DETAILED METRICS BY CLASS:
================================================================================
                           precision    recall  f1-score   support

           0-Maint/Service       0.85      0.78      0.81       156
              1-Diagnosis       0.72      0.69      0.70        89
               2-Battery       0.88      0.92      0.90        45
                 3-Tire       0.79      0.85      0.82       142
    4-Wiper Blade/Arm/Insert   0.91      0.87      0.89        31
             5-Alignment       0.76      0.71      0.73        78
   6-Brake - Pad/Rotor/L/S/D   0.83      0.79      0.81       112
             7-Key Blank       0.95      0.89      0.92        67
                8-Bulb       0.87      0.91      0.89       134
  9-Mount and Balance (BMW)     0.78      0.82      0.80        12
               10-MISC       0.65      0.71      0.68       1687
          11-Belt - Drive       0.89      0.85      0.87        16
      12-Mechanical + Body       0.71      0.68      0.69       542
13-*Not Classified (Exclude)   0.69      0.74      0.71       321
           14-Accessory       0.84      0.79      0.81        52
          15-Spark Plug       0.92      0.88      0.90        13
             16-Hoses       0.87      0.83      0.85         4
       17-Body Collision       0.81      0.77      0.79        29
   18-Transmission Assembly     0.93      0.89      0.91         1
    19-Fluid (All Kinds)       0.96      0.92      0.94         3
       20-Engine Assembly       0.89      0.85      0.87         4

              accuracy                           0.73      1000
             macro avg       0.83      0.81      0.82      1000
          weighted avg       0.73      0.73      0.73      1000

📊 PERFORMANCE INSIGHTS:
========================================
🏆 TOP 3 BEST PERFORMING CLASSES:
  1. 19-Fluid (All Kinds)
     F1: 0.940 | Precision: 0.960 | Recall: 0.920 | Samples: 3
  2. 7-Key Blank
     F1: 0.920 | Precision: 0.950 | Recall: 0.890 | Samples: 67
  3. 18-Transmission Assembly
     F1: 0.910 | Precision: 0.930 | Recall: 0.890 | Samples: 1

⚠️ TOP 3 WORST PERFORMING CLASSES:
  1. 10-MISC
     F1: 0.680 | Precision: 0.650 | Recall: 0.710 | Samples: 1687
  2. 12-Mechanical + Body
     F1: 0.690 | Precision: 0.710 | Recall: 0.680 | Samples: 542
  3. 13-*Not Classified (Exclude)
     F1: 0.710 | Precision: 0.690 | Recall: 0.740 | Samples: 321

🎯 CONFIDENCE ANALYSIS:
📊 Average confidence (correct predictions): 0.847
📊 Average confidence (incorrect predictions): 0.623
📊 Confidence gap: 0.224
📊 High confidence (>0.8) predictions: 567
📊 High confidence accuracy: 0.891
```

---

## 🚀 **Recommendation**

**Use Option 2** (Add New Cell) - it's the cleanest approach and gives you the most comprehensive analysis without modifying your existing working code!

Just copy the code from Option 2 into a new cell after your training completes. 📊
