#!/usr/bin/env python3
"""
Script to fix dependency issues for BERT training
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Success")
            return True
        else:
            print(f"✗ Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def fix_markupsafe():
    """Fix MarkupSafe compatibility issue"""
    print("\n" + "="*50)
    print("FIXING MARKUPSAFE COMPATIBILITY")
    print("="*50)
    
    # Try to downgrade MarkupSafe to a compatible version
    commands = [
        "pip uninstall markupsafe -y",
        "pip install 'markupsafe==2.0.1'",
        "pip install 'jinja2==3.0.3'"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            print(f"Failed to execute: {cmd}")
            return False
    
    return True

def install_compatible_transformers():
    """Install compatible versions of transformers and dependencies"""
    print("\n" + "="*50)
    print("INSTALLING COMPATIBLE TRANSFORMERS")
    print("="*50)
    
    commands = [
        "pip install 'transformers==4.21.0'",
        "pip install 'torch==1.12.0' --index-url https://download.pytorch.org/whl/cpu",
        "pip install 'tokenizers==0.12.1'"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            print(f"Warning: Failed to execute: {cmd}")
    
    return True

def test_imports():
    """Test if the imports work correctly"""
    print("\n" + "="*50)
    print("TESTING IMPORTS")
    print("="*50)
    
    try:
        import torch
        print("✓ torch imported successfully")
        
        from transformers import BertTokenizer, BertForSequenceClassification
        print("✓ transformers imported successfully")
        
        from torch.optim import AdamW
        print("✓ AdamW imported successfully")
        
        # Test loading a small model
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        print("✓ BERT tokenizer loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def create_simple_bert_demo():
    """Create a simple BERT demo that works with the fixed dependencies"""
    print("\n" + "="*50)
    print("CREATING SIMPLE BERT DEMO")
    print("="*50)
    
    demo_code = '''
import pandas as pd
import torch
from transformers import BertTokenizer, BertForSequenceClassification
from torch.optim import AdamW
import warnings
warnings.filterwarnings('ignore')

def simple_bert_demo():
    """Simple BERT demonstration"""
    print("Loading BERT model...")
    
    # Force CPU usage
    device = torch.device('cpu')
    
    # Load tokenizer and model
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    model = BertForSequenceClassification.from_pretrained('bert-base-uncased', num_labels=3)
    model.to(device)
    
    # Sample data
    texts = [
        "oil change service maintenance",
        "brake pad replacement repair", 
        "battery test and diagnosis"
    ]
    labels = [0, 1, 2]  # Maintenance, Repair, Diagnosis
    
    print("Tokenizing texts...")
    # Tokenize
    encodings = tokenizer(texts, truncation=True, padding=True, return_tensors='pt')
    
    print("Running inference...")
    # Simple inference
    model.eval()
    with torch.no_grad():
        outputs = model(**encodings)
        predictions = torch.argmax(outputs.logits, dim=-1)
    
    print("Results:")
    for i, (text, pred) in enumerate(zip(texts, predictions)):
        print(f"  Text: {text}")
        print(f"  Prediction: {pred.item()}")
        print()
    
    print("✓ BERT demo completed successfully!")
    return True

if __name__ == "__main__":
    try:
        simple_bert_demo()
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
'''
    
    with open('simple_bert_demo.py', 'w') as f:
        f.write(demo_code)
    
    print("✓ Created simple_bert_demo.py")
    return True

def main():
    """Main function to fix all dependency issues"""
    print("BERT Dependency Fix Script")
    print("="*50)
    
    # Step 1: Fix MarkupSafe
    if fix_markupsafe():
        print("✓ MarkupSafe fixed")
    else:
        print("✗ MarkupSafe fix failed")
    
    # Step 2: Install compatible transformers
    if install_compatible_transformers():
        print("✓ Compatible transformers installed")
    else:
        print("✗ Transformers installation failed")
    
    # Step 3: Test imports
    if test_imports():
        print("✓ All imports working")
        
        # Step 4: Create demo
        create_simple_bert_demo()
        
        print("\n" + "="*50)
        print("SUCCESS!")
        print("="*50)
        print("Dependencies fixed successfully!")
        print("You can now run: python3 simple_bert_demo.py")
        print("Or try the original BERT training script again.")
        
    else:
        print("\n" + "="*50)
        print("PARTIAL SUCCESS")
        print("="*50)
        print("Some issues remain. You can still use the TF-IDF approach:")
        print("python3 simple_bert_training.py")

if __name__ == "__main__":
    main()
