import pickle
import pandas as pd

def load_model():
    """Load the trained model and components"""
    with open('labor_classifier_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    return model_data['model'], model_data['vectorizer'], model_data['category_mapping'], model_data['model_name']

def predict_labor_category(text, model, vectorizer, category_mapping):
    """Predict labor category for a given text"""
    
    # Transform text using the same vectorizer
    text_tfidf = vectorizer.transform([text])
    
    # Make prediction
    predicted_class = model.predict(text_tfidf)[0]
    
    # Get prediction probability
    if hasattr(model, 'predict_proba'):
        probabilities = model.predict_proba(text_tfidf)[0]
        confidence = max(probabilities)
    else:
        confidence = 1.0
    
    # Get category label
    category_label = category_mapping[category_mapping['category_num'] == predicted_class]['labor_category_label'].iloc[0]
    
    return predicted_class, category_label, confidence

def main():
    print("Loading trained model...")
    model, vectorizer, category_mapping, model_name = load_model()
    
    print(f"Model loaded: {model_name}")
    print(f"Available categories: {len(category_mapping)} categories")
    print("\nCategory Mapping:")
    for _, row in category_mapping.iterrows():
        print(f"  {row['category_num']}: {row['labor_category_label']}")
    
    print("\n" + "="*60)
    print("LABOR DESCRIPTION CLASSIFIER")
    print("="*60)
    
    while True:
        print("\nEnter a labor description (or 'quit' to exit):")
        user_input = input("> ").strip()
        
        if user_input.lower() in ['quit', 'exit', 'q']:
            break
        
        if not user_input:
            print("Please enter a valid description.")
            continue
        
        try:
            pred_num, pred_label, confidence = predict_labor_category(user_input, model, vectorizer, category_mapping)
            
            print(f"\nPrediction Results:")
            print(f"  Input: {user_input}")
            print(f"  Predicted Category: {pred_num} - {pred_label}")
            print(f"  Confidence: {confidence:.4f}")
            
            # Show top 3 predictions if available
            if hasattr(model, 'predict_proba'):
                text_tfidf = vectorizer.transform([user_input])
                probabilities = model.predict_proba(text_tfidf)[0]
                
                # Get top 3 predictions
                top_indices = probabilities.argsort()[-3:][::-1]
                
                print(f"\n  Top 3 Predictions:")
                for i, idx in enumerate(top_indices, 1):
                    category_label = category_mapping[category_mapping['category_num'] == idx]['labor_category_label'].iloc[0]
                    print(f"    {i}. {idx} - {category_label} (confidence: {probabilities[idx]:.4f})")
            
        except Exception as e:
            print(f"Error making prediction: {e}")
    
    print("\nThank you for using the Labor Description Classifier!")

if __name__ == "__main__":
    main()
