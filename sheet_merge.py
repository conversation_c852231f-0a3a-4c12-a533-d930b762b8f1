import pandas as pd


def merge_sheet(file_path):
   

    # Read both sheets
    # sheet1_df = pd.read_excel(file_path, sheet_name="Sheet1")
    # sheet2_df = pd.read_excel(file_path, sheet_name="Sheet2")

    # merged_df = pd.concat([sheet1_df, sheet2_df], ignore_index=True)

    # with pd.ExcelWriter(file_path, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
    #     merged_df.to_excel(writer, sheet_name="MergedSheet", index=False)
    # num_labels = merged_df['labor_category_label'].nunique()
    # print("Number of unique labels:", num_labels)
    # unique_labels = merged_df['labor_category_label'].unique()
    # print("Unique labels:", unique_labels)

    ###################################
    df = pd.read_excel(file_path,sheet_name="MergedSheet")
    num_labels = df['labor_category_label'].nunique()
    print("Number of unique labels:", num_labels)
    unique_labels = df['labor_category_label'].unique()
    print("Unique labels:", unique_labels)
    df['category_num'] = pd.factorize(df['labor_category_label'])[0]
    #print(file_path[['labor_category_label', 'labor_category_label_id']])
    with pd.ExcelWriter(file_path, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
        df.to_excel(writer, sheet_name="MergedSheet", index=False)

merge_sheet("/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/BERTO-DEMO/archive_list_inal.xlsx")